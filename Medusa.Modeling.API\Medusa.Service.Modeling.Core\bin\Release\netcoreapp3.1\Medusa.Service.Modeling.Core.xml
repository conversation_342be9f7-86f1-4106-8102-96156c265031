<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Medusa.Service.Modeling.Core</name>
    </assembly>
    <members>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleClue.MarketObject">
             市场担当对象
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.Production">
             //生产
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.Requirement">
             //客户要求
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.IsDelete">
             //是否删除
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.YSSolution">
             //电池片主栅、细栅印刷解决方案
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.CreateUserOrgPathId">
             //创建用户组织路径Id
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.Capacity">
             //客户产能信息
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.PainSpot">
             //客户痛点
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.CreateDate">
             //创建日期
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.Background">
             //客户需求背景
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.Management">
             //管理
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.Sale">
             //销售
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.CompetitiveProSales">
             //竞品销售
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.TechRoute">
             //组件/电池片技术路线
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.ModifyUserId">
             //修改用户Id
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.TechSource">
             //技术来源
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.SaleClueID">
             //销售线索主表主键
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.AssemblyOtherTape">
             //组件其他胶带
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.ParentId">
             //上级ID
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.MaterialSolution">
             //电池片制程材料解决方案
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.CompetitivePro">
             //竞品产品
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.NewTrend">
             //客户新动向
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.NotMeet">
             //竞品未满足的需求
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.CompetitiveProduction">
             //竞品生产
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.HJSolution">
             //组件浆料、焊接(互联方式)解决方案
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.FZSolution">
             //组件封装解决方案
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.CreateUserId">
             //创建用户Id
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.TerminalRequirement">
             //终端客户需求
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.CompetitiveProManagement">
             //竞品管理
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.ProductName">
             //产品
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.ModifyDate">
             //修改日期
        </member>
        <member name="P:Medusa.Service.Modeling.Core.Entities.SWApp.CRMSaleCluePV.TerminalCuatomer">
             //客户指定终端客户（若有）
        </member>
    </members>
</doc>
