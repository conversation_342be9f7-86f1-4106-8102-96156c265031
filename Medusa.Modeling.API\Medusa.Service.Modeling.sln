Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.29709.97
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Medusa.Service.Modeling.Entrance", "Medusa.Service.Modeling.Entrance\Medusa.Service.Modeling.Entrance.csproj", "{DFF06873-E710-441B-A34D-6DC1BA82D6F1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Medusa.Service.Modeling.Core", "Medusa.Service.Modeling.Core\Medusa.Service.Modeling.Core.csproj", "{8EB68290-5FCB-4875-9700-3E7B7E87EDA0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Medusa.Service.Modeling.Application", "Medusa.Service.Modeling.Application\Medusa.Service.Modeling.Application.csproj", "{057266AE-9B9B-4D64-ACB2-3DAAA20E9552}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Medusa.Service.Modeling.UnitTests", "Medusa.Service.Modeling.UnitTests\Medusa.Service.Modeling.UnitTests.csproj", "{1C41690B-1FB9-45D8-AD3C-957C44A21544}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Medusa.Service.Modeling.Connect", "Medusa.Service.Modeling.Connect\Medusa.Service.Modeling.Connect.csproj", "{85691633-E108-4CA8-94E8-DB30ACE86EBC}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DFF06873-E710-441B-A34D-6DC1BA82D6F1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DFF06873-E710-441B-A34D-6DC1BA82D6F1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DFF06873-E710-441B-A34D-6DC1BA82D6F1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DFF06873-E710-441B-A34D-6DC1BA82D6F1}.Release|Any CPU.Build.0 = Release|Any CPU
		{8EB68290-5FCB-4875-9700-3E7B7E87EDA0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8EB68290-5FCB-4875-9700-3E7B7E87EDA0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8EB68290-5FCB-4875-9700-3E7B7E87EDA0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8EB68290-5FCB-4875-9700-3E7B7E87EDA0}.Release|Any CPU.Build.0 = Release|Any CPU
		{057266AE-9B9B-4D64-ACB2-3DAAA20E9552}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{057266AE-9B9B-4D64-ACB2-3DAAA20E9552}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{057266AE-9B9B-4D64-ACB2-3DAAA20E9552}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{057266AE-9B9B-4D64-ACB2-3DAAA20E9552}.Release|Any CPU.Build.0 = Release|Any CPU
		{1C41690B-1FB9-45D8-AD3C-957C44A21544}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1C41690B-1FB9-45D8-AD3C-957C44A21544}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1C41690B-1FB9-45D8-AD3C-957C44A21544}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1C41690B-1FB9-45D8-AD3C-957C44A21544}.Release|Any CPU.Build.0 = Release|Any CPU
		{85691633-E108-4CA8-94E8-DB30ACE86EBC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{85691633-E108-4CA8-94E8-DB30ACE86EBC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{85691633-E108-4CA8-94E8-DB30ACE86EBC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{85691633-E108-4CA8-94E8-DB30ACE86EBC}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {89F4FB1D-F8B7-4CB5-91E0-E218EEC36382}
	EndGlobalSection
EndGlobal
