{"format": 1, "restore": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\tests\\MT.Enterprise.SDK.Test\\MT.Enterprise.SDK.Test.csproj": {}}, "projects": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.SDK\\MT.Enterprise.SDK.csproj": {"version": "2.0.4", "restore": {"projectUniqueName": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.SDK\\MT.Enterprise.SDK.csproj", "projectName": "MT.Enterprise.SDK", "projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.SDK\\MT.Enterprise.SDK.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.SDK\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\BPM\\BPM平台\\Pakages": {}, "D:\\项目\\Bpm_Chinahho\\BPM\\NugetPakage": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"DotNetCore.CAP": {"target": "Package", "version": "[3.1.2, )"}, "DotNetCore.CAP.Dashboard": {"target": "Package", "version": "[3.1.2, )"}, "DotNetCore.CAP.MySql": {"target": "Package", "version": "[3.1.2, )"}, "DotNetCore.CAP.RabbitMQ": {"target": "Package", "version": "[3.1.2, )"}, "DotNetCore.CAP.SqlServer": {"target": "Package", "version": "[3.1.2, )"}, "HttpClientFactory": {"target": "Package", "version": "[1.0.3, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.2.88, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[1.1.118, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.303\\RuntimeIdentifierGraph.json"}}}, "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\tests\\MT.Enterprise.SDK.Test\\MT.Enterprise.SDK.Test.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\tests\\MT.Enterprise.SDK.Test\\MT.Enterprise.SDK.Test.csproj", "projectName": "MT.Enterprise.SDK.Test", "projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\tests\\MT.Enterprise.SDK.Test\\MT.Enterprise.SDK.Test.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\tests\\MT.Enterprise.SDK.Test\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\BPM\\BPM平台\\Pakages": {}, "D:\\项目\\Bpm_Chinahho\\BPM\\NugetPakage": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.SDK\\MT.Enterprise.SDK.csproj": {"projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.SDK\\MT.Enterprise.SDK.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[16.6.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}, "xunit": {"target": "Package", "version": "[2.4.1, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.4.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.303\\RuntimeIdentifierGraph.json"}}}}}