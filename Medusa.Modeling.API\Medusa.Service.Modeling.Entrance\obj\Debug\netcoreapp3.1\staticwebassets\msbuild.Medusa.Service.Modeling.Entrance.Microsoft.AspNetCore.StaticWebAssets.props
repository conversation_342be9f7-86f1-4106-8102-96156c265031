﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\.gitkeep))">
      <SourceType>Package</SourceType>
      <SourceId>Medusa.Service.Modeling.Entrance</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Medusa.Service.Modeling.Entrance</BasePath>
      <RelativePath>.gitkeep</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory></CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\.gitkeep))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>