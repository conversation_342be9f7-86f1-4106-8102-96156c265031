{"version": 2, "dgSpecHash": "urrEly+x0+g=", "success": false, "projectFilePath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\Medusa.Modeling.API\\Medusa.Service.Modeling.Application\\Medusa.Service.Modeling.Application.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\aspectcore.abstractions\\2.2.0\\aspectcore.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspectcore.core\\2.2.0\\aspectcore.core.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspectcore.extensions.dependencyinjection\\2.1.0\\aspectcore.extensions.dependencyinjection.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspectcore.extensions.reflection\\2.2.0\\aspectcore.extensions.reflection.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\automapper\\9.0.0\\automapper.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\consul\\1.6.1.1\\consul.1.6.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dotnetcore.cap\\3.1.2\\dotnetcore.cap.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dotnetcore.cap.dashboard\\3.1.2\\dotnetcore.cap.dashboard.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dotnetcore.cap.mysql\\3.1.2\\dotnetcore.cap.mysql.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dotnetcore.cap.rabbitmq\\3.1.2\\dotnetcore.cap.rabbitmq.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dotnetcore.cap.sqlserver\\3.1.2\\dotnetcore.cap.sqlserver.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dotnetcore.npoi\\1.2.1\\dotnetcore.npoi.1.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dotnetcore.npoi.core\\1.2.1\\dotnetcore.npoi.core.1.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dotnetcore.npoi.openxml4net\\1.2.1\\dotnetcore.npoi.openxml4net.1.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dotnetcore.npoi.openxmlformats\\1.2.1\\dotnetcore.npoi.openxmlformats.1.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\easycaching.core\\0.8.9\\easycaching.core.0.8.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\easycaching.inmemory\\0.8.9\\easycaching.inmemory.0.8.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\exceptionless\\5.0.0\\exceptionless.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\exceptionless.nlog\\5.0.0\\exceptionless.nlog.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fastexpressioncompiler\\1.10.1\\fastexpressioncompiler.1.10.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\flee\\1.2.1\\flee.1.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.protobuf\\3.19.4\\google.protobuf.3.19.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.protobuf\\3.21.2\\google.protobuf.3.21.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.core\\2.46.3\\grpc.core.2.46.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.core.api\\2.46.3\\grpc.core.api.2.46.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\httpclientfactory\\1.0.3\\httpclientfactory.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\k4os.compression.lz4\\1.2.6\\k4os.compression.lz4.1.2.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\k4os.compression.lz4.streams\\1.2.6\\k4os.compression.lz4.streams.1.2.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\k4os.hash.xxhash\\1.0.6\\k4os.hash.xxhash.1.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.antiforgery\\2.2.0\\microsoft.aspnetcore.antiforgery.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.abstractions\\2.2.0\\microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.core\\2.2.0\\microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\2.2.0\\microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization.policy\\2.2.0\\microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cors\\2.2.0\\microsoft.aspnetcore.cors.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.internal\\2.2.0\\microsoft.aspnetcore.cryptography.internal.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection\\2.2.0\\microsoft.aspnetcore.dataprotection.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.abstractions\\2.2.0\\microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.diagnostics.abstractions\\2.2.0\\microsoft.aspnetcore.diagnostics.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting\\2.2.7\\microsoft.aspnetcore.hosting.2.2.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.server.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.html.abstractions\\2.2.0\\microsoft.aspnetcore.html.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http\\2.2.0\\microsoft.aspnetcore.http.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.extensions\\2.2.0\\microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.jsonpatch\\2.2.0\\microsoft.aspnetcore.jsonpatch.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.localization\\2.2.0\\microsoft.aspnetcore.localization.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc\\2.2.0\\microsoft.aspnetcore.mvc.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.abstractions\\2.2.0\\microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.analyzers\\2.2.0\\microsoft.aspnetcore.mvc.analyzers.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.apiexplorer\\2.2.0\\microsoft.aspnetcore.mvc.apiexplorer.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.core\\2.2.5\\microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.cors\\2.2.0\\microsoft.aspnetcore.mvc.cors.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.dataannotations\\2.2.0\\microsoft.aspnetcore.mvc.dataannotations.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.formatters.json\\2.2.0\\microsoft.aspnetcore.mvc.formatters.json.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.localization\\2.2.0\\microsoft.aspnetcore.mvc.localization.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor\\2.2.0\\microsoft.aspnetcore.mvc.razor.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor.extensions\\2.2.0\\microsoft.aspnetcore.mvc.razor.extensions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razorpages\\2.2.0\\microsoft.aspnetcore.mvc.razorpages.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.taghelpers\\2.2.0\\microsoft.aspnetcore.mvc.taghelpers.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.viewfeatures\\2.2.0\\microsoft.aspnetcore.mvc.viewfeatures.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.razor\\2.2.0\\microsoft.aspnetcore.razor.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.razor.design\\2.2.0\\microsoft.aspnetcore.razor.design.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.razor.language\\2.2.0\\microsoft.aspnetcore.razor.language.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.razor.runtime\\2.2.0\\microsoft.aspnetcore.razor.runtime.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.responsecaching.abstractions\\2.2.0\\microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.routing\\2.2.0\\microsoft.aspnetcore.routing.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.routing.abstractions\\2.2.0\\microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.webutilities\\2.2.0\\microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\1.1.1\\microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.hashcode\\1.1.0\\microsoft.bcl.hashcode.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\1.1.0\\microsoft.codeanalysis.analyzers.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\2.8.0\\microsoft.codeanalysis.common.2.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\2.8.0\\microsoft.codeanalysis.csharp.2.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.razor\\2.2.0\\microsoft.codeanalysis.razor.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.5.0\\microsoft.csharp.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient\\2.0.0\\microsoft.data.sqlclient.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient\\2.1.4\\microsoft.data.sqlclient.2.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient.sni.runtime\\2.0.0\\microsoft.data.sqlclient.sni.runtime.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient.sni.runtime\\2.1.1\\microsoft.data.sqlclient.sni.runtime.2.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlite\\5.0.5\\microsoft.data.sqlite.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlite.core\\5.0.5\\microsoft.data.sqlite.core.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\3.1.7\\microsoft.entityframeworkcore.3.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\3.1.7\\microsoft.entityframeworkcore.abstractions.3.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.analyzers\\3.1.7\\microsoft.entityframeworkcore.analyzers.3.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\3.1.7\\microsoft.entityframeworkcore.relational.3.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\3.1.7\\microsoft.extensions.caching.abstractions.3.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\3.1.7\\microsoft.extensions.caching.memory.3.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\5.0.0\\microsoft.extensions.configuration.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\5.0.0\\microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\3.1.7\\microsoft.extensions.configuration.binder.3.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\2.2.4\\microsoft.extensions.configuration.environmentvariables.2.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\5.0.0\\microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\5.0.0\\microsoft.extensions.configuration.json.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\3.1.7\\microsoft.extensions.dependencyinjection.3.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\3.1.15\\microsoft.extensions.dependencyinjection.abstractions.3.1.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\3.1.0\\microsoft.extensions.dependencymodel.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\5.0.0\\microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.composite\\2.2.0\\microsoft.extensions.fileproviders.composite.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\5.0.0\\microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\5.0.0\\microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\3.1.15\\microsoft.extensions.hosting.abstractions.3.1.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http\\3.1.0\\microsoft.extensions.http.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization\\2.2.0\\microsoft.extensions.localization.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization.abstractions\\2.2.0\\microsoft.extensions.localization.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\3.1.7\\microsoft.extensions.logging.3.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\2.2.0\\microsoft.extensions.logging.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\3.1.7\\microsoft.extensions.logging.abstractions.3.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\3.1.15\\microsoft.extensions.logging.abstractions.3.1.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\6.0.0\\microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\2.2.0\\microsoft.extensions.objectpool.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\3.1.7\\microsoft.extensions.options.3.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\3.1.0\\microsoft.extensions.options.configurationextensions.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.platformabstractions\\1.1.0\\microsoft.extensions.platformabstractions.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\5.0.0\\microsoft.extensions.primitives.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.webencoders\\2.2.0\\microsoft.extensions.webencoders.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.14.0\\microsoft.identity.client.4.14.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.21.1\\microsoft.identity.client.4.21.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\5.6.0\\microsoft.identitymodel.jsonwebtokens.5.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\6.6.0\\microsoft.identitymodel.jsonwebtokens.6.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\6.8.0\\microsoft.identitymodel.jsonwebtokens.6.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\5.6.0\\microsoft.identitymodel.logging.5.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\6.6.0\\microsoft.identitymodel.logging.6.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\6.8.0\\microsoft.identitymodel.logging.6.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\5.6.0\\microsoft.identitymodel.protocols.5.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\6.8.0\\microsoft.identitymodel.protocols.6.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\5.6.0\\microsoft.identitymodel.protocols.openidconnect.5.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\6.8.0\\microsoft.identitymodel.protocols.openidconnect.6.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\5.6.0\\microsoft.identitymodel.tokens.5.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\6.6.0\\microsoft.identitymodel.tokens.6.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\6.8.0\\microsoft.identitymodel.tokens.6.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.http.headers\\2.2.0\\microsoft.net.http.headers.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.0.1\\microsoft.netcore.platforms.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.1\\microsoft.netcore.platforms.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\2.0.0\\microsoft.netcore.platforms.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\3.1.0\\microsoft.netcore.platforms.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\3.1.1\\microsoft.netcore.platforms.3.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.0.1\\microsoft.netcore.targets.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.3\\microsoft.netcore.targets.1.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.primitives\\4.0.1\\microsoft.win32.primitives.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.0.0\\microsoft.win32.registry.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.5.0\\microsoft.win32.registry.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\4.5.0\\microsoft.win32.systemevents.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\4.7.0\\microsoft.win32.systemevents.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.bson\\2.4.4\\mongodb.bson.2.4.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.driver\\2.4.4\\mongodb.driver.2.4.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.driver.core\\2.4.4\\mongodb.driver.core.2.4.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mono.cecil\\0.11.3\\mono.cecil.0.11.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mt.sqlsugar\\1.3.2\\mt.sqlsugar.1.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mysql.data\\8.0.31\\mysql.data.8.0.31.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mysqlconnector\\1.0.1\\mysqlconnector.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nacos-sdk-csharp\\1.3.5\\nacos-sdk-csharp.1.3.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nacos-sdk-csharp-unofficial\\0.8.5\\nacos-sdk-csharp-unofficial.0.8.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nacos-sdk-csharp-unofficial.aspnetcore\\0.8.5\\nacos-sdk-csharp-unofficial.aspnetcore.0.8.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nacos-sdk-csharp.aspnetcore\\1.3.5\\nacos-sdk-csharp.aspnetcore.1.3.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\1.6.1\\netstandard.library.1.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\12.0.1\\newtonsoft.json.12.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\12.0.3\\newtonsoft.json.12.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.2\\newtonsoft.json.13.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json.bson\\1.0.1\\newtonsoft.json.bson.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nlog\\5.1.3\\nlog.5.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nlog.database\\5.0.5\\nlog.database.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nlog.extensions.logging\\5.2.3\\nlog.extensions.logging.5.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nlog.mongodb.netcore\\1.0.0\\nlog.mongodb.netcore.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nlog.web.aspnetcore\\5.2.3\\nlog.web.aspnetcore.5.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql\\7.0.0\\npgsql.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oracle.manageddataaccess.core\\3.21.1\\oracle.manageddataaccess.core.3.21.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oscar.data.sqlclient\\4.0.4\\oscar.data.sqlclient.4.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pipelines.sockets.unofficial\\2.2.2\\pipelines.sockets.unofficial.2.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\portable.bouncycastle\\1.9.0\\portable.bouncycastle.1.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\rabbitmq.client\\6.2.1\\rabbitmq.client.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.0.0\\runtime.native.system.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.net.security\\4.0.1\\runtime.native.system.net.security.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography\\4.0.0\\runtime.native.system.security.cryptography.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpziplib\\1.0.0\\sharpziplib.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\smartformat.net\\2.4.2\\smartformat.net.2.4.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.bundle_e_sqlite3\\2.0.4\\sqlitepclraw.bundle_e_sqlite3.2.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.core\\2.0.4\\sqlitepclraw.core.2.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.lib.e_sqlite3\\2.0.4\\sqlitepclraw.lib.e_sqlite3.2.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.provider.dynamic_cdecl\\2.0.4\\sqlitepclraw.provider.dynamic_cdecl.2.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlsugarcore.dm\\1.0.0\\sqlsugarcore.dm.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlsugarcore.kdbndp\\6.1.0\\sqlsugarcore.kdbndp.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stackexchange.redis\\2.6.90\\stackexchange.redis.2.6.90.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stylecop.analyzers\\1.1.118\\stylecop.analyzers.1.1.118.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.appcontext\\4.3.0\\system.appcontext.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.3.0\\system.buffers.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.0\\system.buffers.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.0.11\\system.collections.4.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.0.12\\system.collections.concurrent.4.0.12.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\6.0.0\\system.collections.immutable.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.nongeneric\\4.0.1\\system.collections.nongeneric.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.nongeneric\\4.3.0\\system.collections.nongeneric.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.specialized\\4.0.1\\system.collections.specialized.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.specialized\\4.3.0\\system.collections.specialized.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.annotations\\4.7.0\\system.componentmodel.annotations.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.primitives\\4.3.0\\system.componentmodel.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.typeconverter\\4.3.0\\system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\4.4.1\\system.configuration.configurationmanager.4.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\4.7.0\\system.configuration.configurationmanager.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\6.0.0\\system.configuration.configurationmanager.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.common\\4.3.0\\system.data.common.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.oledb\\6.0.0\\system.data.oledb.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.0.11\\system.diagnostics.debug.4.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\4.3.0\\system.diagnostics.diagnosticsource.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\4.5.0\\system.diagnostics.diagnosticsource.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\4.5.1\\system.diagnostics.diagnosticsource.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\4.7.0\\system.diagnostics.diagnosticsource.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\4.7.1\\system.diagnostics.diagnosticsource.4.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\6.0.0\\system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.fileversioninfo\\4.3.0\\system.diagnostics.fileversioninfo.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\4.7.0\\system.diagnostics.performancecounter.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\6.0.0\\system.diagnostics.performancecounter.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.process\\4.1.0\\system.diagnostics.process.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.stacktrace\\4.3.0\\system.diagnostics.stacktrace.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracesource\\4.0.0\\system.diagnostics.tracesource.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.1.0\\system.diagnostics.tracing.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices\\4.7.0\\system.directoryservices.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices.protocols\\4.7.0\\system.directoryservices.protocols.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\4.5.0\\system.drawing.common.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\4.7.0\\system.drawing.common.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.dynamic.runtime\\4.3.0\\system.dynamic.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.0.11\\system.globalization.4.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.0.1\\system.globalization.extensions.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\5.6.0\\system.identitymodel.tokens.jwt.5.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\6.6.0\\system.identitymodel.tokens.jwt.6.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\6.8.0\\system.identitymodel.tokens.jwt.6.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.1.0\\system.io.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.compression.zipfile\\4.3.0\\system.io.compression.zipfile.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.0.1\\system.io.filesystem.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.accesscontrol\\4.7.0\\system.io.filesystem.accesscontrol.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.0.1\\system.io.filesystem.primitives.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\5.0.1\\system.io.pipelines.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.1.0\\system.linq.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.dynamic.core\\1.0.9\\system.linq.dynamic.core.1.0.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.queryable\\4.0.1\\system.linq.queryable.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.3\\system.memory.4.5.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http\\4.3.0\\system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.nameresolution\\4.0.0\\system.net.nameresolution.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.nameresolution\\4.3.0\\system.net.nameresolution.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.primitives\\4.0.11\\system.net.primitives.4.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.security\\4.0.0\\system.net.security.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.sockets\\4.3.0\\system.net.sockets.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.private.datacontractserialization\\4.3.0\\system.private.datacontractserialization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.private.uri\\4.3.2\\system.private.uri.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.1.0\\system.reflection.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.7.0\\system.reflection.emit.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.0.1\\system.reflection.emit.ilgeneration.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.7.0\\system.reflection.emit.ilgeneration.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.0.1\\system.reflection.emit.lightweight.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.7.0\\system.reflection.emit.lightweight.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.extensions\\4.0.1\\system.reflection.extensions.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\6.0.1\\system.reflection.metadata.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.0.1\\system.reflection.primitives.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.0.1\\system.resources.resourcemanager.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.1.0\\system.runtime.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.caching\\4.7.0\\system.runtime.caching.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.5.0\\system.runtime.compilerservices.unsafe.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.5.2\\system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\5.0.0\\system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.1.0\\system.runtime.extensions.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.0.1\\system.runtime.handles.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.1.0\\system.runtime.interopservices.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.serialization.formatters\\4.3.0\\system.runtime.serialization.formatters.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.serialization.json\\4.3.0\\system.runtime.serialization.json.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.serialization.primitives\\4.3.0\\system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\4.5.0\\system.security.accesscontrol.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\4.7.0\\system.security.accesscontrol.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\5.0.0\\system.security.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.claims\\4.0.1\\system.security.claims.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\4.3.0\\system.security.cryptography.cng.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\4.5.0\\system.security.cryptography.cng.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\4.5.0\\system.security.cryptography.pkcs.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.0.0\\system.security.cryptography.primitives.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\4.4.0\\system.security.cryptography.protecteddata.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\4.7.0\\system.security.cryptography.protecteddata.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\6.0.0\\system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\4.5.0\\system.security.cryptography.xml.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\4.5.0\\system.security.permissions.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\4.7.0\\system.security.permissions.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal\\4.0.1\\system.security.principal.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.0.0\\system.security.principal.windows.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.5.0\\system.security.principal.windows.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.7.0\\system.security.principal.windows.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.securestring\\4.0.0\\system.security.securestring.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.securestring\\4.3.0\\system.security.securestring.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.0.11\\system.text.encoding.4.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\4.3.0\\system.text.encoding.codepages.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\4.4.0\\system.text.encoding.codepages.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\4.5.0\\system.text.encoding.codepages.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\4.7.0\\system.text.encoding.codepages.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\4.7.1\\system.text.encoding.codepages.4.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.extensions\\4.0.11\\system.text.encoding.extensions.4.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\4.5.0\\system.text.encodings.web.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\4.7.0\\system.text.json.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\4.7.1\\system.text.json.4.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\5.0.0\\system.text.json.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.regularexpressions\\4.3.0\\system.text.regularexpressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.0.11\\system.threading.4.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.channels\\4.7.1\\system.threading.channels.4.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.0.11\\system.threading.tasks.4.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.3.0\\system.threading.tasks.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.1\\system.threading.tasks.extensions.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.parallel\\4.3.0\\system.threading.tasks.parallel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.thread\\4.3.0\\system.threading.thread.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.threadpool\\4.0.10\\system.threading.threadpool.4.0.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.timer\\4.3.0\\system.threading.timer.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.valuetuple\\4.3.0\\system.valuetuple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\4.7.0\\system.windows.extensions.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xmldocument\\4.3.0\\system.xml.xmldocument.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xmlserializer\\4.3.0\\system.xml.xmlserializer.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xpath\\4.3.0\\system.xml.xpath.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xpath.xdocument\\4.3.0\\system.xml.xpath.xdocument.4.3.0.nupkg.sha512"], "logs": [{"code": "NU1106", "level": "Error", "message": "无法满足“System.Text.Json”的互相冲突的请求: System.Text.Json (>= 5.0.0) (通过 package/Microsoft.Extensions.Configuration.Json 5.0.0), System.Text.Json (>= 4.7.1) (通过 package/Oracle.ManagedDataAccess.Core 3.21.1), System.Text.Json (>= 4.7.1) (通过 package/Oracle.ManagedDataAccess.Core 3.21.1), System.Text.Json (>= 4.7.0) (通过 package/Microsoft.Extensions.DependencyModel 3.1.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Text.Json", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.Extensions.Logging.Abstractions”的互相冲突的请求: Microsoft.Extensions.Logging.Abstractions (>= 3.1.15) (通过 package/Microsoft.Extensions.Hosting.Abstractions 3.1.15), Microsoft.Extensions.Logging.Abstractions (>= 2.2.0) (通过 package/Microsoft.AspNetCore.Mvc.Core 2.2.5), Microsoft.Extensions.Logging.Abstractions (>= 3.1.7) (通过 package/DotNetCore.CAP.Dashboard 3.1.2), Microsoft.Extensions.Logging.Abstractions (>= 6.0.0) (通过 package/Npgsql 7.0.0), Microsoft.Extensions.Logging.Abstractions (>= 6.0.0) (通过 package/Npgsql 7.0.0), Microsoft.Extensions.Logging.Abstractions (>= 3.1.7) (通过 package/Microsoft.Extensions.Caching.Memory 3.1.7), Microsoft.Extensions.Logging.Abstractions (>= 3.1.7) (通过 package/Microsoft.Extensions.Logging 3.1.7), Microsoft.Extensions.Logging.Abstractions (>= 3.1.7) (通过 package/Microsoft.Extensions.Caching.Memory 3.1.7), Microsoft.Extensions.Logging.Abstractions (>= 3.1.7) (通过 package/Microsoft.Extensions.Logging 3.1.7), Microsoft.Extensions.Logging.Abstractions (>= 2.2.0) (通过 package/Microsoft.AspNetCore.Cors 2.2.0), Microsoft.Extensions.Logging.Abstractions (>= 2.2.0) (通过 package/Microsoft.Extensions.Localization 2.2.0), Microsoft.Extensions.Logging.Abstractions (>= 2.2.0) (通过 package/Microsoft.AspNetCore.Localization 2.2.0), Microsoft.Extensions.Logging.Abstractions (>= 2.2.0) (通过 package/Microsoft.Extensions.Localization 2.2.0), Microsoft.Extensions.Logging.Abstractions (>= 2.2.0) (通过 package/Microsoft.AspNetCore.DataProtection 2.2.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.Extensions.Logging.Abstractions", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Newtonsoft.Json”的互相冲突的请求: Newtonsoft.Json (>= 12.0.1) (通过 package/SmartFormat.NET 2.4.2), Newtonsoft.Json (>= 13.0.2) (通过 project/MT.Enterprise.Core.ShuiWu 1.0.16.4), Newtonsoft.Json (>= 13.0.2) (通过 project/MT.Enterprise.SDK.ShuiWu 2.0.4), Newtonsoft.Json (>= 13.0.2) (通过 project/MT.Enterprise.Utils.ShuiWu 1.0.13), Newtonsoft.Json (>= 13.0.2) (通过 project/MT.Enterprise.BPM.Reactor.ShuiWu 1.0.37), Newtonsoft.Json (>= 13.0.2) (通过 project/MT.SqlSugar 1.3.2), Newtonsoft.Json (>= 12.0.3) (通过 package/nacos-sdk-csharp-unofficial 0.8.5) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Newtonsoft.Json", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.IdentityModel.Tokens.Jwt”的互相冲突的请求: System.IdentityModel.Tokens.Jwt (>= 6.6.0) (通过 project/MT.Enterprise.Utils.ShuiWu 1.0.13), System.IdentityModel.Tokens.Jwt (>= 6.8.0) (通过 package/Microsoft.IdentityModel.Protocols.OpenIdConnect 6.8.0), System.IdentityModel.Tokens.Jwt (>= 6.8.0) (通过 package/Microsoft.IdentityModel.Protocols.OpenIdConnect 6.8.0), System.IdentityModel.Tokens.Jwt (>= 5.6.0) (通过 package/Microsoft.IdentityModel.Protocols.OpenIdConnect 5.6.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.IdentityModel.Tokens.Jwt", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.Data.Sqlite”的互相冲突的请求: Microsoft.Data.Sqlite (>= 5.0.5) (通过 project/MT.SqlSugar 1.3.2), Microsoft.Data.Sqlite (>= 5.0.5) (通过 package/MT.SqlSugar 1.3.2) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.Data.Sqlite", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“MySql.Data”的互相冲突的请求: MySql.Data (>= 8.0.31) (通过 project/MT.SqlSugar 1.3.2), MySql.Data (>= 8.0.31) (通过 package/MT.SqlSugar 1.3.2) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "MySql.Data", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Npgsql”的互相冲突的请求: Npgsql (>= 7.0.0) (通过 project/MT.SqlSugar 1.3.2), Npgsql (>= 7.0.0) (通过 package/MT.SqlSugar 1.3.2) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Npgsql", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Oracle.ManagedDataAccess.Core”的互相冲突的请求: Oracle.ManagedDataAccess.Core (>= 3.21.1) (通过 project/MT.SqlSugar 1.3.2), Oracle.ManagedDataAccess.Core (>= 3.21.1) (通过 package/MT.SqlSugar 1.3.2) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Oracle.ManagedDataAccess.Core", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Oscar.Data.SqlClient”的互相冲突的请求: Oscar.Data.SqlClient (>= 4.0.4) (通过 project/MT.SqlSugar 1.3.2), Oscar.Data.SqlClient (>= 4.0.4) (通过 package/MT.SqlSugar 1.3.2) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Oscar.Data.SqlClient", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“SqlSugarCore.Dm”的互相冲突的请求: SqlSugarCore.Dm (>= 1.0.0) (通过 project/MT.SqlSugar 1.3.2), SqlSugarCore.Dm (>= 1.0.0) (通过 package/MT.SqlSugar 1.3.2) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "SqlSugarCore.Dm", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“SqlSugarCore.Kdbndp”的互相冲突的请求: SqlSugarCore.Kdbndp (>= 6.1.0) (通过 project/MT.SqlSugar 1.3.2), SqlSugarCore.Kdbndp (>= 6.1.0) (通过 package/MT.SqlSugar 1.3.2) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "SqlSugarCore.Kdbndp", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Data.Common”的互相冲突的请求: System.Data.Common (>= 4.3.0) (通过 project/MT.SqlSugar 1.3.2), System.Data.Common (>= 4.3.0) (通过 package/MT.SqlSugar 1.3.2) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Data.Common", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.Data.SqlClient”的互相冲突的请求: Microsoft.Data.SqlClient (>= 2.1.4) (通过 project/MT.SqlSugar 1.3.2), Microsoft.Data.SqlClient (>= 2.1.4) (通过 package/MT.SqlSugar 1.3.2), Microsoft.Data.SqlClient (>= 2.0.0) (通过 package/DotNetCore.CAP.SqlServer 3.1.2) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.Data.SqlClient", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Data.OleDb”的互相冲突的请求: System.Data.OleDb (>= 6.0.0) (通过 project/MT.SqlSugar 1.3.2), System.Data.OleDb (>= 6.0.0) (通过 package/MT.SqlSugar 1.3.2) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Data.OleDb", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Reflection.Emit.Lightweight”的互相冲突的请求: System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 project/MT.SqlSugar 1.3.2), System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 package/MT.SqlSugar 1.3.2), System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 package/Flee 1.2.1), System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 package/FastExpressionCompiler 1.10.1), System.Reflection.Emit.Lightweight (>= 4.7.0) (通过 package/Exceptionless 5.0.0), System.Reflection.Emit.Lightweight (>= 4.0.1) (通过 package/MongoDB.Bson 2.4.4), System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.Lightweight (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Reflection.Emit.Lightweight", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.CSharp”的互相冲突的请求: Microsoft.CSharp (>= 4.7.0) (通过 package/AspectCore.Core 2.2.0), Microsoft.CSharp (>= 4.5.0) (通过 package/AutoMapper 9.0.0), Microsoft.CSharp (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.6.0), Microsoft.CSharp (>= 4.5.0) (通过 package/Microsoft.Identity.Client 4.14.0), Microsoft.CSharp (>= 4.5.0) (通过 package/Microsoft.AspNetCore.JsonPatch 2.2.0), Microsoft.CSharp (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), Microsoft.CSharp (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), Microsoft.CSharp (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), Microsoft.CSharp (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), Microsoft.CSharp (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), Microsoft.CSharp (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.CSharp", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Reflection.Emit”的互相冲突的请求: System.Reflection.Emit (>= 4.7.0) (通过 package/AspectCore.Core 2.2.0), System.Reflection.Emit (>= 4.3.0) (通过 package/AutoMapper 9.0.0), System.Reflection.Emit (>= 4.3.0) (通过 package/Flee 1.2.1), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Linq.Dynamic.Core 1.0.9), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Xml.XmlSerializer 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Reflection.Emit", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Reflection.Emit.ILGeneration”的互相冲突的请求: System.Reflection.Emit.ILGeneration (>= 4.7.0) (通过 package/AspectCore.Core 2.2.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/Flee 1.2.1), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.0.1) (通过 package/System.Reflection.Emit.Lightweight 4.0.1), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Emit.ILGeneration (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Reflection.Emit.ILGeneration", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Diagnostics.DiagnosticSource”的互相冲突的请求: System.Diagnostics.DiagnosticSource (>= 4.5.0) (通过 package/Microsoft.AspNetCore.Mvc.Core 2.2.5), System.Diagnostics.DiagnosticSource (>= 4.5.1) (通过 package/Microsoft.AspNetCore.Hosting 2.2.7), System.Diagnostics.DiagnosticSource (>= 4.7.1) (通过 package/DotNetCore.CAP 3.1.2), System.Diagnostics.DiagnosticSource (>= 6.0.0) (通过 package/Npgsql 7.0.0), System.Diagnostics.DiagnosticSource (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), System.Diagnostics.DiagnosticSource (>= 4.7.0) (通过 package/EasyCaching.Core 0.8.9), System.Diagnostics.DiagnosticSource (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), System.Diagnostics.DiagnosticSource (>= 6.0.0) (通过 package/Npgsql 7.0.0), System.Diagnostics.DiagnosticSource (>= 4.7.1) (通过 package/Microsoft.EntityFrameworkCore 3.1.7), System.Diagnostics.DiagnosticSource (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.0.0), System.Diagnostics.DiagnosticSource (>= 4.7.1) (通过 package/Microsoft.EntityFrameworkCore 3.1.7), System.Diagnostics.DiagnosticSource (>= 4.3.0) (通过 package/System.Net.Http 4.3.0), System.Diagnostics.DiagnosticSource (>= 4.3.0) (通过 package/System.Net.Http 4.3.0), System.Diagnostics.DiagnosticSource (>= 4.3.0) (通过 package/System.Net.Http 4.3.0), System.Diagnostics.DiagnosticSource (>= 4.3.0) (通过 package/System.Net.Http 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Diagnostics.DiagnosticSource", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Threading.Tasks.Extensions”的互相冲突的请求: System.Threading.Tasks.Extensions (>= 4.5.1) (通过 package/Microsoft.AspNetCore.Mvc.Core 2.2.5), System.Threading.Tasks.Extensions (>= 4.5.4) (通过 package/Exceptionless 5.0.0), System.Threading.Tasks.Extensions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Threading.Tasks.Extensions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Threading.Tasks.Extensions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Threading.Tasks.Extensions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Threading.Tasks.Extensions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Threading.Tasks.Extensions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Threading.Tasks.Extensions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Threading.Tasks.Extensions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Threading.Tasks.Extensions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Threading.Tasks.Extensions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Threading.Tasks.Extensions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Threading.Tasks.Extensions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Threading.Tasks.Extensions", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.IdentityModel.JsonWebTokens”的互相冲突的请求: Microsoft.IdentityModel.JsonWebTokens (>= 6.6.0) (通过 package/System.IdentityModel.Tokens.Jwt 6.6.0), Microsoft.IdentityModel.JsonWebTokens (>= 6.8.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), Microsoft.IdentityModel.JsonWebTokens (>= 6.8.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), Microsoft.IdentityModel.JsonWebTokens (>= 5.6.0) (通过 package/Microsoft.Data.SqlClient 2.0.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.IdentityModel.JsonWebTokens", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.IdentityModel.Tokens”的互相冲突的请求: Microsoft.IdentityModel.Tokens (>= 6.6.0) (通过 package/System.IdentityModel.Tokens.Jwt 6.6.0), Microsoft.IdentityModel.Tokens (>= 6.8.0) (通过 package/Microsoft.IdentityModel.JsonWebTokens 6.8.0), Microsoft.IdentityModel.Tokens (>= 6.8.0) (通过 package/Microsoft.IdentityModel.JsonWebTokens 6.8.0), Microsoft.IdentityModel.Tokens (>= 5.6.0) (通过 package/Microsoft.IdentityModel.JsonWebTokens 5.6.0), Microsoft.IdentityModel.Tokens (>= 6.8.0) (通过 package/Microsoft.IdentityModel.Protocols 6.8.0), Microsoft.IdentityModel.Tokens (>= 6.8.0) (通过 package/System.IdentityModel.Tokens.Jwt 6.8.0), Microsoft.IdentityModel.Tokens (>= 6.8.0) (通过 package/Microsoft.IdentityModel.Protocols 6.8.0), Microsoft.IdentityModel.Tokens (>= 6.8.0) (通过 package/System.IdentityModel.Tokens.Jwt 6.8.0), Microsoft.IdentityModel.Tokens (>= 5.6.0) (通过 package/Microsoft.IdentityModel.Protocols 5.6.0), Microsoft.IdentityModel.Tokens (>= 5.6.0) (通过 package/System.IdentityModel.Tokens.Jwt 5.6.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.IdentityModel.Tokens", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.ComponentModel”的互相冲突的请求: System.ComponentModel (>= 4.3.0) (通过 package/Flee 1.2.1), System.ComponentModel (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.ComponentModel", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Reflection”的互相冲突的请求: System.Reflection (>= 4.3.0) (通过 package/Flee 1.2.1), System.Reflection (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Reflection (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Reflection (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Reflection (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.Reflection (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Reflection (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Reflection (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.Reflection (>= 4.3.0) (通过 package/System.Resources.ResourceManager 4.3.0), System.Reflection (>= 4.3.0) (通过 package/System.Resources.ResourceManager 4.3.0), System.Reflection (>= 4.1.0) (通过 package/System.Linq.Queryable 4.0.1), System.Reflection (>= 4.3.0) (通过 package/System.Reflection.TypeExtensions 4.3.0), System.Reflection (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Reflection (>= 4.1.0) (通过 package/System.Reflection.Emit.Lightweight 4.0.1), System.Reflection (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0), System.Reflection (>= 4.3.0) (通过 package/System.Runtime.Serialization.Formatters 4.3.0), System.Reflection (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.Reflection (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Reflection (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Reflection (>= 4.1.0) (通过 package/System.Resources.ResourceManager 4.0.1), System.Reflection (>= 4.1.0) (通过 package/System.Resources.ResourceManager 4.0.1), System.Reflection (>= 4.1.0) (通过 package/System.Runtime.InteropServices 4.1.0), System.Reflection (>= 4.1.0) (通过 package/System.Resources.ResourceManager 4.0.1), System.Reflection (>= 4.1.0) (通过 package/System.Resources.ResourceManager 4.0.1), System.Reflection (>= 4.1.0) (通过 package/System.Resources.ResourceManager 4.0.1), System.Reflection (>= 4.1.0) (通过 package/System.Runtime.InteropServices 4.1.0), System.Reflection (>= 4.1.0) (通过 package/System.Security.Principal.Windows 4.0.0), System.Reflection (>= 4.1.0) (通过 package/System.Collections.Concurrent 4.0.12), System.Reflection (>= 4.1.0) (通过 package/System.Resources.ResourceManager 4.0.1), System.Reflection (>= 4.1.0) (通过 package/System.Runtime.InteropServices 4.1.0), System.Reflection (>= 4.1.0) (通过 package/System.Resources.ResourceManager 4.0.1), System.Reflection (>= 4.1.0) (通过 package/System.Runtime.InteropServices 4.1.0), System.Reflection (>= 4.3.0) (通过 package/System.Resources.ResourceManager 4.3.0), System.Reflection (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Reflection (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Reflection (>= 4.3.0) (通过 package/System.Resources.ResourceManager 4.3.0), System.Reflection (>= 4.3.0) (通过 package/System.Resources.ResourceManager 4.3.0), System.Reflection (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Reflection (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Reflection (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Reflection (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Reflection (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Reflection (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Reflection (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Reflection (>= 4.1.0) (通过 package/System.Runtime.InteropServices 4.1.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Reflection", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.Data.Sqlite.Core”的互相冲突的请求: Microsoft.Data.Sqlite.Core (>= 5.0.5) (通过 package/Microsoft.Data.Sqlite 5.0.5), Microsoft.Data.Sqlite.Core (>= 5.0.5) (通过 package/Microsoft.Data.Sqlite 5.0.5) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.Data.Sqlite.Core", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“SQLitePCLRaw.bundle_e_sqlite3”的互相冲突的请求: SQLitePCLRaw.bundle_e_sqlite3 (>= 2.0.4) (通过 package/Microsoft.Data.Sqlite 5.0.5), SQLitePCLRaw.bundle_e_sqlite3 (>= 2.0.4) (通过 package/Microsoft.Data.Sqlite 5.0.5) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "SQLitePCLRaw.bundle_e_sqlite3", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Google.Protobuf”的互相冲突的请求: Google.Protobuf (>= 3.19.4) (通过 package/MySql.Data 8.0.31), Google.Protobuf (>= 3.19.4) (通过 package/MySql.Data 8.0.31), Google.Protobuf (>= 3.21.2) (通过 package/nacos-sdk-csharp 1.3.5) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Google.Protobuf", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“K4os.Compression.LZ4.Streams”的互相冲突的请求: K4os.Compression.LZ4.Streams (>= 1.2.6) (通过 package/MySql.Data 8.0.31), K4os.Compression.LZ4.Streams (>= 1.2.6) (通过 package/MySql.Data 8.0.31) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "K4os.Compression.LZ4.Streams", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Portable.BouncyCastle”的互相冲突的请求: Portable.BouncyCastle (>= 1.9.0) (通过 package/MySql.Data 8.0.31), Portable.BouncyCastle (>= 1.9.0) (通过 package/MySql.Data 8.0.31) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Portable.BouncyCastle", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Buffers”的互相冲突的请求: System.Buffers (>= 4.5.1) (通过 package/MySql.Data 8.0.31), System.Buffers (>= 4.5.0) (通过 package/Microsoft.AspNetCore.Http.Extensions 2.2.0), System.Buffers (>= 4.5.1) (通过 package/MySql.Data 8.0.31), System.Buffers (>= 4.5.0) (通过 package/Microsoft.AspNetCore.Http.Extensions 2.2.0), System.Buffers (>= 4.5.1) (通过 package/Microsoft.Extensions.Logging.Abstractions 6.0.0), System.Buffers (>= 4.5.0) (通过 package/Microsoft.Net.Http.Headers 2.2.0), System.Buffers (>= 4.5.0) (通过 package/Microsoft.Net.Http.Headers 2.2.0), System.Buffers (>= 4.5.1) (通过 package/Microsoft.Extensions.Logging.Abstractions 6.0.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression.ZipFile 4.3.0), System.Buffers (>= 4.5.0) (通过 package/Microsoft.Net.Http.Headers 2.2.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression.ZipFile 4.3.0), System.Buffers (>= 4.5.0) (通过 package/Microsoft.AspNetCore.Http.Extensions 2.2.0), System.Buffers (>= 4.5.0) (通过 package/Microsoft.AspNetCore.Http.Extensions 2.2.0), System.Buffers (>= 4.5.0) (通过 package/Microsoft.AspNetCore.Http.Extensions 2.2.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression.ZipFile 4.3.0), System.Buffers (>= 4.5.0) (通过 package/Microsoft.Net.Http.Headers 2.2.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression.ZipFile 4.3.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Buffers (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Buffers", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Configuration.ConfigurationManager”的互相冲突的请求: System.Configuration.ConfigurationManager (>= 4.4.1) (通过 package/MySql.Data 8.0.31), System.Configuration.ConfigurationManager (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), System.Configuration.ConfigurationManager (>= 6.0.0) (通过 package/System.Data.OleDb 6.0.0), System.Configuration.ConfigurationManager (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), System.Configuration.ConfigurationManager (>= 4.4.1) (通过 package/MySql.Data 8.0.31), System.Configuration.ConfigurationManager (>= 6.0.0) (通过 package/System.Data.OleDb 6.0.0), System.Configuration.ConfigurationManager (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.0.0), System.Configuration.ConfigurationManager (>= 4.7.0) (通过 package/System.Diagnostics.PerformanceCounter 4.7.0), System.Configuration.ConfigurationManager (>= 4.7.0) (通过 package/System.Diagnostics.PerformanceCounter 4.7.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Configuration.ConfigurationManager", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Runtime.CompilerServices.Unsafe”的互相冲突的请求: System.Runtime.CompilerServices.Unsafe (>= 5.0.0) (通过 package/MySql.Data 8.0.31), System.Runtime.CompilerServices.Unsafe (>= 6.0.0) (通过 package/Npgsql 7.0.0), System.Runtime.CompilerServices.Unsafe (>= 5.0.0) (通过 package/MySql.Data 8.0.31), System.Runtime.CompilerServices.Unsafe (>= 6.0.0) (通过 package/Npgsql 7.0.0), System.Runtime.CompilerServices.Unsafe (>= 4.5.2) (通过 package/Google.Protobuf 3.21.2), System.Runtime.CompilerServices.Unsafe (>= 4.5.0) (通过 package/System.Text.Encoding.CodePages 4.5.0), System.Runtime.CompilerServices.Unsafe (>= 6.0.0) (通过 package/System.Collections.Immutable 6.0.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Runtime.CompilerServices.Unsafe", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Security.Permissions”的互相冲突的请求: System.Security.Permissions (>= 4.7.0) (通过 package/MySql.Data 8.0.31), System.Security.Permissions (>= 4.7.0) (通过 package/MySql.Data 8.0.31), System.Security.Permissions (>= 4.7.0) (通过 package/System.DirectoryServices 4.7.0), System.Security.Permissions (>= 4.7.0) (通过 package/System.Configuration.ConfigurationManager 4.7.0), System.Security.Permissions (>= 6.0.0) (通过 package/System.Configuration.ConfigurationManager 6.0.0), System.Security.Permissions (>= 4.7.0) (通过 package/System.Configuration.ConfigurationManager 4.7.0), System.Security.Permissions (>= 4.7.0) (通过 package/System.DirectoryServices 4.7.0), System.Security.Permissions (>= 6.0.0) (通过 package/System.Configuration.ConfigurationManager 6.0.0), System.Security.Permissions (>= 4.7.0) (通过 package/System.Configuration.ConfigurationManager 4.7.0), System.Security.Permissions (>= 4.7.0) (通过 package/System.Configuration.ConfigurationManager 4.7.0), System.Security.Permissions (>= 4.7.0) (通过 package/System.Configuration.ConfigurationManager 4.7.0), System.Security.Permissions (>= 4.5.0) (通过 package/System.Security.Cryptography.Xml 4.5.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Security.Permissions", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Text.Encoding.CodePages”的互相冲突的请求: System.Text.Encoding.CodePages (>= 4.4.0) (通过 package/MySql.Data 8.0.31), System.Text.Encoding.CodePages (>= 4.7.1) (通过 package/Oscar.Data.SqlClient 4.0.4), System.Text.Encoding.CodePages (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), System.Text.Encoding.CodePages (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), System.Text.Encoding.CodePages (>= 4.4.0) (通过 package/MySql.Data 8.0.31), System.Text.Encoding.CodePages (>= 4.7.1) (通过 package/Oscar.Data.SqlClient 4.0.4), System.Text.Encoding.CodePages (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.0.0), System.Text.Encoding.CodePages (>= 4.5.0) (通过 package/DotNetCore.NPOI.Core 1.2.1), System.Text.Encoding.CodePages (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding.CodePages (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding.CodePages (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding.CodePages (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding.CodePages (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding.CodePages (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding.CodePages (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Text.Encoding.CodePages", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Diagnostics.PerformanceCounter”的互相冲突的请求: System.Diagnostics.PerformanceCounter (>= 4.7.0) (通过 package/Oracle.ManagedDataAccess.Core 3.21.1), System.Diagnostics.PerformanceCounter (>= 6.0.0) (通过 package/System.Data.OleDb 6.0.0), System.Diagnostics.PerformanceCounter (>= 4.7.0) (通过 package/Oracle.ManagedDataAccess.Core 3.21.1), System.Diagnostics.PerformanceCounter (>= 6.0.0) (通过 package/System.Data.OleDb 6.0.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Diagnostics.PerformanceCounter", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.DirectoryServices”的互相冲突的请求: System.DirectoryServices (>= 4.7.0) (通过 package/Oracle.ManagedDataAccess.Core 3.21.1), System.DirectoryServices (>= 4.7.0) (通过 package/Oracle.ManagedDataAccess.Core 3.21.1) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.DirectoryServices", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.DirectoryServices.Protocols”的互相冲突的请求: System.DirectoryServices.Protocols (>= 4.7.0) (通过 package/Oracle.ManagedDataAccess.Core 3.21.1), System.DirectoryServices.Protocols (>= 4.7.0) (通过 package/Oracle.ManagedDataAccess.Core 3.21.1) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.DirectoryServices.Protocols", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Collections”的互相冲突的请求: System.Collections (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Collections (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Collections (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Collections (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Collections (>= 4.0.11) (通过 package/System.Linq.Queryable 4.0.1), System.Collections (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Collections (>= 4.0.11) (通过 package/System.Diagnostics.Process 4.1.0), System.Collections (>= 4.0.11) (通过 package/System.Diagnostics.TraceSource 4.0.0), System.Collections (>= 4.0.11) (通过 package/System.Net.NameResolution 4.0.0), System.Collections (>= 4.0.11) (通过 package/System.Net.Security 4.0.0), System.Collections (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0), System.Collections (>= 4.3.0) (通过 package/System.Net.NameResolution 4.3.0), System.Collections (>= 4.3.0) (通过 package/System.Runtime.Serialization.Formatters 4.3.0), System.Collections (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.Collections (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Collections (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Collections (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Collections (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Collections (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Collections (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Collections (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Collections (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Collections (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Collections", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Globalization”的互相冲突的请求: System.Globalization (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Globalization (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Globalization (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Globalization (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Globalization (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Globalization (>= 4.0.11) (通过 package/System.Resources.ResourceManager 4.0.1), System.Globalization (>= 4.0.11) (通过 package/System.Collections.NonGeneric 4.0.1), System.Globalization (>= 4.0.11) (通过 package/System.Diagnostics.Process 4.1.0), System.Globalization (>= 4.0.11) (通过 package/System.Collections.Specialized 4.0.1), System.Globalization (>= 4.0.11) (通过 package/System.Diagnostics.TraceSource 4.0.0), System.Globalization (>= 4.0.11) (通过 package/System.Net.NameResolution 4.0.0), System.Globalization (>= 4.0.11) (通过 package/System.Net.Security 4.0.0), System.Globalization (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0), System.Globalization (>= 4.3.0) (通过 package/System.Net.NameResolution 4.3.0), System.Globalization (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.Globalization (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Globalization (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Globalization (>= 4.0.11) (通过 package/System.Resources.ResourceManager 4.0.1), System.Globalization (>= 4.0.11) (通过 package/System.Security.Cryptography.Primitives 4.0.0), System.Globalization (>= 4.3.0) (通过 package/System.Resources.ResourceManager 4.3.0), System.Globalization (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Globalization (>= 4.3.0) (通过 package/System.Resources.ResourceManager 4.3.0), System.Globalization (>= 4.3.0) (通过 package/System.Resources.ResourceManager 4.3.0), System.Globalization (>= 4.3.0) (通过 package/System.Security.Cryptography.Primitives 4.3.0), System.Globalization (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Globalization (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Globalization (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Globalization (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Globalization (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Globalization (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Globalization", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.IO”的互相冲突的请求: System.IO (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.IO (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.IO (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.IO (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.IO (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.IO (>= 4.1.0) (通过 package/System.Reflection 4.1.0), System.IO (>= 4.1.0) (通过 package/System.Diagnostics.Process 4.1.0), System.IO (>= 4.1.0) (通过 package/System.Net.Security 4.0.0), System.IO (>= 4.3.0) (通过 package/System.Runtime.Serialization.Json 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.1.0) (通过 package/System.Reflection 4.1.0), System.IO (>= 4.1.0) (通过 package/System.Security.Cryptography.Primitives 4.0.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Primitives 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Console 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XmlDocument 4.3.0), System.IO (>= 4.1.0) (通过 package/System.Reflection 4.1.0), System.IO (>= 4.1.0) (通过 package/System.Reflection 4.1.0), System.IO (>= 4.1.0) (通过 package/System.Reflection 4.1.0), System.IO (>= 4.1.0) (通过 package/System.Reflection 4.1.0), System.IO (>= 4.1.0) (通过 package/System.Reflection 4.1.0), System.IO (>= 4.1.0) (通过 package/System.Reflection 4.1.0), System.IO (>= 4.1.0) (通过 package/System.Security.Claims 4.0.1), System.IO (>= 4.1.0) (通过 package/System.Reflection 4.1.0), System.IO (>= 4.1.0) (通过 package/System.Reflection 4.1.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Console 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XmlDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Console 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XmlDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Console 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XmlDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Console 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XmlDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Console 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XmlDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Console 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.IO (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XmlDocument 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Primitives 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XPath 4.3.0), System.IO (>= 4.1.0) (通过 package/System.Reflection 4.1.0), System.IO (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Primitives 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XPath 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Primitives 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XPath 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Primitives 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XPath 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Primitives 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XPath 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Primitives 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XPath 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Security.Cryptography.Primitives 4.3.0), System.IO (>= 4.3.0) (通过 package/System.Xml.XPath 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.IO", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Resources.ResourceManager”的互相冲突的请求: System.Resources.ResourceManager (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Resources.ResourceManager (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Resources.ResourceManager (>= 4.0.1) (通过 package/System.Linq.Queryable 4.0.1), System.Resources.ResourceManager (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Resources.ResourceManager (>= 4.0.1) (通过 package/System.Collections.NonGeneric 4.0.1), System.Resources.ResourceManager (>= 4.0.1) (通过 package/System.Diagnostics.Process 4.1.0), System.Resources.ResourceManager (>= 4.0.1) (通过 package/System.Collections.Specialized 4.0.1), System.Resources.ResourceManager (>= 4.0.1) (通过 package/System.Diagnostics.TraceSource 4.0.0), System.Resources.ResourceManager (>= 4.0.1) (通过 package/System.Net.NameResolution 4.0.0), System.Resources.ResourceManager (>= 4.0.1) (通过 package/System.Net.Security 4.0.0), System.Resources.ResourceManager (>= 4.0.1) (通过 package/System.Security.SecureString 4.0.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/System.Net.NameResolution 4.3.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/System.Runtime.Serialization.Formatters 4.3.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/System.Runtime.Serialization.Primitives 4.3.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/System.Security.SecureString 4.3.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Resources.ResourceManager (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Resources.ResourceManager (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Resources.ResourceManager", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Runtime”的互相冲突的请求: System.Runtime (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Runtime (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.ComponentModel 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Reflection.Emit.ILGeneration 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Runtime (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.Runtime (>= 4.1.0) (通过 package/System.Linq.Queryable 4.0.1), System.Runtime (>= 4.3.0) (通过 package/System.Reflection.TypeExtensions 4.3.0), System.Runtime (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime (>= 4.1.0) (通过 package/System.Collections.NonGeneric 4.0.1), System.Runtime (>= 4.1.0) (通过 package/System.Diagnostics.Process 4.1.0), System.Runtime (>= 4.1.0) (通过 package/System.Reflection.Emit.Lightweight 4.0.1), System.Runtime (>= 4.1.0) (通过 package/System.Collections.Specialized 4.0.1), System.Runtime (>= 4.1.0) (通过 package/System.Diagnostics.TraceSource 4.0.0), System.Runtime (>= 4.1.0) (通过 package/System.Net.NameResolution 4.0.0), System.Runtime (>= 4.1.0) (通过 package/System.Net.Security 4.0.0), System.Runtime (>= 4.1.0) (通过 package/System.Security.SecureString 4.0.0), System.Runtime (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Net.NameResolution 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Runtime.Serialization.Formatters 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Runtime.Serialization.Json 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Runtime.Serialization.Primitives 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Security.SecureString 4.3.0), System.Runtime (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.Runtime (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Runtime", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Runtime.Extensions”的互相冲突的请求: System.Runtime.Extensions (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Runtime.Extensions (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Runtime.Extensions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime.Extensions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime.Extensions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime.Extensions (>= 4.1.0) (通过 package/System.Linq 4.1.0), System.Runtime.Extensions (>= 4.1.0) (通过 package/System.Collections.NonGeneric 4.0.1), System.Runtime.Extensions (>= 4.1.0) (通过 package/System.Diagnostics.Process 4.1.0), System.Runtime.Extensions (>= 4.1.0) (通过 package/System.Collections.Specialized 4.0.1), System.Runtime.Extensions (>= 4.1.0) (通过 package/System.Diagnostics.TraceSource 4.0.0), System.Runtime.Extensions (>= 4.1.0) (通过 package/System.Net.NameResolution 4.0.0), System.Runtime.Extensions (>= 4.1.0) (通过 package/System.Net.Security 4.0.0), System.Runtime.Extensions (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0), System.Runtime.Extensions (>= 4.3.0) (通过 package/System.Net.NameResolution 4.3.0), System.Runtime.Extensions (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.Runtime.Extensions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime.Extensions (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Runtime.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Runtime.Extensions", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Text.RegularExpressions”的互相冲突的请求: System.Text.RegularExpressions (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Text.RegularExpressions (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Text.RegularExpressions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Text.RegularExpressions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Text.RegularExpressions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Text.RegularExpressions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Text.RegularExpressions (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Text.RegularExpressions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Text.RegularExpressions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Text.RegularExpressions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Text.RegularExpressions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Text.RegularExpressions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Text.RegularExpressions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Text.RegularExpressions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Text.RegularExpressions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Text.RegularExpressions", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Threading.Tasks”的互相冲突的请求: System.Threading.Tasks (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.Data.Common 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Threading.Tasks (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.0.11) (通过 package/System.Diagnostics.Process 4.1.0), System.Threading.Tasks (>= 4.0.11) (通过 package/System.Net.NameResolution 4.0.0), System.Threading.Tasks (>= 4.0.11) (通过 package/System.Net.Security 4.0.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.Net.NameResolution 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Threading.Tasks (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Threading.Tasks (>= 4.0.11) (通过 package/System.Threading 4.0.11), System.Threading.Tasks (>= 4.0.11) (通过 package/System.Threading 4.0.11), System.Threading.Tasks (>= 4.0.11) (通过 package/System.Threading 4.0.11), System.Threading.Tasks (>= 4.0.11) (通过 package/System.Security.Cryptography.Primitives 4.0.0), System.Threading.Tasks (>= 4.0.11) (通过 package/System.Threading 4.0.11), System.Threading.Tasks (>= 4.3.0) (通过 package/System.Threading 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.Security.Cryptography.Primitives 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.Threading 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.Threading 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Threading.Tasks (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Threading.Tasks (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Threading.Tasks (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Threading.Tasks (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Threading.Tasks (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Threading.Tasks (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Threading.Tasks (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Threading.Tasks (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Threading.Tasks (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Threading.Tasks (>= 4.0.11) (通过 package/System.IO 4.1.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Threading.Tasks", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.Data.SqlClient.SNI.runtime”的互相冲突的请求: Microsoft.Data.SqlClient.SNI.runtime (>= 2.1.1) (通过 package/Microsoft.Data.SqlClient 2.1.4), Microsoft.Data.SqlClient.SNI.runtime (>= 2.1.1) (通过 package/Microsoft.Data.SqlClient 2.1.4), Microsoft.Data.SqlClient.SNI.runtime (>= 2.0.0) (通过 package/Microsoft.Data.SqlClient 2.0.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.Data.SqlClient.SNI.runtime", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.Win32.Registry”的互相冲突的请求: Microsoft.Win32.Registry (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), Microsoft.Win32.Registry (>= 5.0.0) (通过 package/System.Data.OleDb 6.0.0), Microsoft.Win32.Registry (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), Microsoft.Win32.Registry (>= 5.0.0) (通过 package/System.Data.OleDb 6.0.0), Microsoft.Win32.Registry (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.0.0), Microsoft.Win32.Registry (>= 4.7.0) (通过 package/System.Diagnostics.PerformanceCounter 4.7.0), Microsoft.Win32.Registry (>= 4.7.0) (通过 package/System.Diagnostics.PerformanceCounter 4.7.0), Microsoft.Win32.Registry (>= 4.0.0) (通过 package/System.Diagnostics.Process 4.1.0), Microsoft.Win32.Registry (>= 4.5.0) (通过 package/Microsoft.AspNetCore.DataProtection 2.2.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.Win32.Registry", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Security.Principal.Windows”的互相冲突的请求: System.Security.Principal.Windows (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), System.Security.Principal.Windows (>= 5.0.0) (通过 package/System.Data.OleDb 6.0.0), System.Security.Principal.Windows (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), System.Security.Principal.Windows (>= 5.0.0) (通过 package/System.Data.OleDb 6.0.0), System.Security.Principal.Windows (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.0.0), System.Security.Principal.Windows (>= 4.7.0) (通过 package/System.Diagnostics.PerformanceCounter 4.7.0), System.Security.Principal.Windows (>= 4.7.0) (通过 package/System.DirectoryServices 4.7.0), System.Security.Principal.Windows (>= 4.7.0) (通过 package/System.DirectoryServices.Protocols 4.7.0), System.Security.Principal.Windows (>= 4.7.0) (通过 package/System.Diagnostics.PerformanceCounter 4.7.0), System.Security.Principal.Windows (>= 4.7.0) (通过 package/System.DirectoryServices 4.7.0), System.Security.Principal.Windows (>= 4.7.0) (通过 package/System.DirectoryServices.Protocols 4.7.0), System.Security.Principal.Windows (>= 4.7.0) (通过 package/System.Security.AccessControl 4.7.0), System.Security.Principal.Windows (>= 4.7.0) (通过 package/System.Security.AccessControl 4.7.0), System.Security.Principal.Windows (>= 4.0.0) (通过 package/System.Net.NameResolution 4.0.0), System.Security.Principal.Windows (>= 4.5.0) (通过 package/Microsoft.AspNetCore.DataProtection 2.2.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Security.Principal.Windows", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Runtime.Caching”的互相冲突的请求: System.Runtime.Caching (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), System.Runtime.Caching (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), System.Runtime.Caching (>= 4.7.0) (通过 package/Microsoft.Data.SqlClient 2.0.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Runtime.Caching", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.Identity.Client”的互相冲突的请求: Microsoft.Identity.Client (>= 4.21.1) (通过 package/Microsoft.Data.SqlClient 2.1.4), Microsoft.Identity.Client (>= 4.21.1) (通过 package/Microsoft.Data.SqlClient 2.1.4), Microsoft.Identity.Client (>= 4.14.0) (通过 package/Microsoft.Data.SqlClient 2.0.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.Identity.Client", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.IdentityModel.Protocols.OpenIdConnect”的互相冲突的请求: Microsoft.IdentityModel.Protocols.OpenIdConnect (>= 6.8.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), Microsoft.IdentityModel.Protocols.OpenIdConnect (>= 6.8.0) (通过 package/Microsoft.Data.SqlClient 2.1.4), Microsoft.IdentityModel.Protocols.OpenIdConnect (>= 5.6.0) (通过 package/Microsoft.Data.SqlClient 2.0.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.IdentityModel.Protocols.OpenIdConnect", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Memory”的互相冲突的请求: System.Memory (>= 4.5.4) (通过 package/System.Data.OleDb 6.0.0), System.Memory (>= 4.5.4) (通过 package/System.Data.OleDb 6.0.0), System.Memory (>= 4.5.4) (通过 package/RabbitMQ.Client 6.2.1), System.Memory (>= 4.5.3) (通过 package/Google.Protobuf 3.19.4), System.Memory (>= 4.5.4) (通过 package/Microsoft.Extensions.Logging.Abstractions 6.0.0), System.Memory (>= 4.5.4) (通过 package/System.Diagnostics.DiagnosticSource 6.0.0), System.Memory (>= 4.5.3) (通过 package/Google.Protobuf 3.19.4), System.Memory (>= 4.5.4) (通过 package/Microsoft.Extensions.Logging.Abstractions 6.0.0), System.Memory (>= 4.5.4) (通过 package/System.Diagnostics.DiagnosticSource 6.0.0), System.Memory (>= 4.5.3) (通过 package/Google.Protobuf 3.21.2), System.Memory (>= 4.5.3) (通过 package/Grpc.Core 2.46.3), System.Memory (>= 4.5.3) (通过 package/SQLitePCLRaw.core 2.0.4), System.Memory (>= 4.5.3) (通过 package/SQLitePCLRaw.core 2.0.4), System.Memory (>= 4.5.4) (通过 package/K4os.Compression.LZ4 1.2.6), System.Memory (>= 4.5.3) (通过 package/K4os.Hash.xxHash 1.0.6), System.Memory (>= 4.5.4) (通过 package/System.Collections.Immutable 6.0.0), System.Memory (>= 4.5.3) (通过 package/SQLitePCLRaw.core 2.0.4), System.Memory (>= 4.5.3) (通过 package/SQLitePCLRaw.core 2.0.4), System.Memory (>= 4.5.4) (通过 package/K4os.Compression.LZ4 1.2.6), System.Memory (>= 4.5.3) (通过 package/K4os.Hash.xxHash 1.0.6) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Memory", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Reflection.Primitives”的互相冲突的请求: System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection.Emit.ILGeneration 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection.Emit.Lightweight 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection.Emit 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Reflection 4.1.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Reflection.Emit.Lightweight 4.0.1), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Runtime.InteropServices 4.1.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Runtime.InteropServices 4.1.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Runtime.InteropServices 4.1.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Runtime.InteropServices 4.1.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Reflection 4.1.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Reflection 4.1.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Runtime.InteropServices 4.1.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Reflection 4.1.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Reflection 4.1.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Reflection 4.1.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Reflection 4.1.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Reflection 4.1.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Reflection 4.1.0), System.Reflection.Primitives (>= 4.0.1) (通过 package/System.Reflection 4.1.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Reflection 4.3.0), System.Reflection.Primitives (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Reflection.Primitives", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.NETCore.Platforms”的互相冲突的请求: Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/NETStandard.Library 1.6.1), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/NETStandard.Library 1.6.1), Microsoft.NETCore.Platforms (>= 2.0.0) (通过 package/System.Text.Encoding.CodePages 4.4.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Diagnostics.PerformanceCounter 4.7.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.DirectoryServices 4.7.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.DirectoryServices.Protocols 4.7.0), Microsoft.NETCore.Platforms (>= 3.1.1) (通过 package/System.Text.Encoding.CodePages 4.7.1), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Text.Encoding.CodePages 4.7.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Text.Encoding.CodePages 4.7.0), Microsoft.NETCore.Platforms (>= 2.0.0) (通过 package/System.Text.Encoding.CodePages 4.4.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Diagnostics.PerformanceCounter 4.7.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.DirectoryServices 4.7.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.DirectoryServices.Protocols 4.7.0), Microsoft.NETCore.Platforms (>= 3.1.1) (通过 package/System.Text.Encoding.CodePages 4.7.1), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Text.Encoding.CodePages 4.7.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 2.0.0) (通过 package/System.Drawing.Common 4.5.0), Microsoft.NETCore.Platforms (>= 2.0.0) (通过 package/System.Text.Encoding.CodePages 4.5.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Security.AccessControl 4.7.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Security.AccessControl 4.7.0), Microsoft.NETCore.Platforms (>= 5.0.0) (通过 package/System.Security.AccessControl 5.0.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/NETStandard.Library 1.6.1), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Security.AccessControl 4.7.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Security.AccessControl 4.7.0), Microsoft.NETCore.Platforms (>= 5.0.0) (通过 package/System.Security.AccessControl 5.0.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Collections 4.0.11), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Diagnostics.Debug 4.0.11), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Reflection 4.1.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Reflection.Extensions 4.0.1), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Resources.ResourceManager 4.0.1), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Runtime 4.1.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Diagnostics.Process 4.1.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Diagnostics.TraceSource 4.0.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Net.NameResolution 4.0.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Net.Security 4.0.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Security.SecureString 4.0.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Security.AccessControl 4.7.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Net.NameResolution 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.1) (通过 package/System.Private.Uri 4.3.2), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.SecureString 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/NETStandard.Library 1.6.1), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Drawing.Common 4.7.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Security.AccessControl 4.7.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Security.AccessControl 4.7.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Drawing.Common 4.7.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Runtime.Extensions 4.1.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Diagnostics.Debug 4.0.11), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Globalization 4.0.11), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Resources.ResourceManager 4.0.1), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Runtime 4.1.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Runtime.Extensions 4.1.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Reflection 4.1.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Reflection.Primitives 4.0.1), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Runtime 4.1.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Globalization 4.0.11), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Globalization.Extensions 4.0.1), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Resources.ResourceManager 4.0.1), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Runtime 4.1.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Runtime.Extensions 4.1.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Security.AccessControl 4.7.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 2.0.0) (通过 package/System.Security.Principal.Windows 4.5.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.Compression 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Drawing.Common 4.7.0), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Drawing.Common 4.7.0), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Threading.Tasks 4.0.11), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Diagnostics.Debug 4.0.11), Microsoft.NETCore.Platforms (>= 1.0.1) (通过 package/System.Threading.Tasks 4.0.11), Microsoft.NETCore.Platforms (>= 3.1.0) (通过 package/System.Drawing.Common 4.7.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Globalization.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.Compression 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.Compression 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.Compression 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.Compression 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.Compression 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.FileVersionInfo 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.Compression 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Platforms (>= 2.0.0) (通过 package/System.Security.AccessControl 4.5.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 2.0.0) (通过 package/System.Security.AccessControl 4.5.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Platforms (>= 1.1.0) (通过 package/System.IO 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.NETCore.Platforms", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.Win32.Primitives”的互相冲突的请求: Microsoft.Win32.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), Microsoft.Win32.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), Microsoft.Win32.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), Microsoft.Win32.Primitives (>= 4.0.1) (通过 package/System.Diagnostics.Process 4.1.0), Microsoft.Win32.Primitives (>= 4.0.1) (通过 package/System.Net.Security 4.0.0), Microsoft.Win32.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), Microsoft.Win32.Primitives (>= 4.0.1) (通过 package/System.Security.Principal.Windows 4.0.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.Win32.Primitives", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Collections.Concurrent”的互相冲突的请求: System.Collections.Concurrent (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Collections.Concurrent (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Collections.Concurrent (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Collections.Concurrent (>= 4.0.12) (通过 package/System.Net.Security 4.0.0), System.Collections.Concurrent (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Collections.Concurrent (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Collections.Concurrent (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Collections.Concurrent (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Collections.Concurrent (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Collections.Concurrent (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Collections.Concurrent (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Collections.Concurrent (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Collections.Concurrent (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Collections.Concurrent", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Diagnostics.Debug”的互相冲突的请求: System.Diagnostics.Debug (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Diagnostics.Debug (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Diagnostics.Debug (>= 4.0.11) (通过 package/System.Linq.Queryable 4.0.1), System.Diagnostics.Debug (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Diagnostics.Debug (>= 4.0.11) (通过 package/System.Collections.NonGeneric 4.0.1), System.Diagnostics.Debug (>= 4.0.11) (通过 package/System.Diagnostics.Process 4.1.0), System.Diagnostics.Debug (>= 4.0.11) (通过 package/System.Diagnostics.TraceSource 4.0.0), System.Diagnostics.Debug (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.Diagnostics.Debug (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Diagnostics.Debug (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Diagnostics.Debug (>= 4.0.11) (通过 package/System.Collections.NonGeneric 4.0.1), System.Diagnostics.Debug (>= 4.0.11) (通过 package/System.Security.Principal.Windows 4.0.0), System.Diagnostics.Debug (>= 4.0.11) (通过 package/System.Collections.Concurrent 4.0.12), System.Diagnostics.Debug (>= 4.0.11) (通过 package/System.Security.Cryptography.Primitives 4.0.0), System.Diagnostics.Debug (>= 4.0.11) (通过 package/System.Security.Cryptography.Primitives 4.0.0), System.Diagnostics.Debug (>= 4.3.0) (通过 package/System.Collections.NonGeneric 4.3.0), System.Diagnostics.Debug (>= 4.3.0) (通过 package/System.Linq 4.3.0), System.Diagnostics.Debug (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Diagnostics.Debug (>= 4.3.0) (通过 package/System.Security.Cryptography.Primitives 4.3.0), System.Diagnostics.Debug (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Diagnostics.Debug (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Diagnostics.Debug (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Diagnostics.Debug (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Diagnostics.Debug (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Diagnostics.Debug (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Diagnostics.Debug", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Diagnostics.Tools”的互相冲突的请求: System.Diagnostics.Tools (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Diagnostics.Tools (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Diagnostics.Tools (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Diagnostics.Tools (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.Diagnostics.Tools (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Diagnostics.Tools (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Diagnostics.Tools (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Diagnostics.Tools (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Diagnostics.Tools (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Diagnostics.Tools (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Diagnostics.Tools (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Diagnostics.Tools (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Diagnostics.Tools", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Diagnostics.Tracing”的互相冲突的请求: System.Diagnostics.Tracing (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Diagnostics.Tracing (>= 4.1.0) (通过 package/System.Net.NameResolution 4.0.0), System.Diagnostics.Tracing (>= 4.1.0) (通过 package/System.Net.Security 4.0.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Net.NameResolution 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Collections.Concurrent 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Threading.Tasks.Parallel 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Collections.Concurrent 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Collections.Concurrent 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Threading.Tasks.Parallel 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Collections.Concurrent 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Threading.Tasks.Parallel 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Collections.Concurrent 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Threading.Tasks.Parallel 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Collections.Concurrent 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Threading.Tasks.Parallel 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Collections.Concurrent 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Threading.Tasks.Parallel 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Collections.Concurrent 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Threading.Tasks.Parallel 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Buffers 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Buffers 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Buffers 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Buffers 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Buffers 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Buffers 4.3.0), System.Diagnostics.Tracing (>= 4.3.0) (通过 package/System.Buffers 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Diagnostics.Tracing", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.IO.FileSystem”的互相冲突的请求: System.IO.FileSystem (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.IO.FileSystem (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.IO.FileSystem (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.IO.FileSystem (>= 4.0.1) (通过 package/System.Diagnostics.Process 4.1.0), System.IO.FileSystem (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.IO.FileSystem (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.IO.FileSystem (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.IO.FileSystem", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.IO.FileSystem.Primitives”的互相冲突的请求: System.IO.FileSystem.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.IO.FileSystem.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.IO.FileSystem.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.IO.FileSystem.Primitives (>= 4.0.1) (通过 package/System.Diagnostics.Process 4.1.0), System.IO.FileSystem.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.IO.FileSystem.Primitives (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem.Primitives (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.IO.FileSystem.Primitives (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem.Primitives (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem.Primitives (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem.Primitives (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem.Primitives (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem.Primitives (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.IO.FileSystem.Primitives (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.IO.FileSystem.Primitives", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Linq”的互相冲突的请求: System.Linq (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Linq (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Linq (>= 4.1.0) (通过 package/System.Linq.Queryable 4.0.1), System.Linq (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Linq (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0), System.Linq (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Linq (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Linq (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Linq (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Linq (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Linq (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Linq (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Linq (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Linq (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Linq", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Net.Primitives”的互相冲突的请求: System.Net.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Net.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Net.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Net.Primitives (>= 4.0.11) (通过 package/System.Net.NameResolution 4.0.0), System.Net.Primitives (>= 4.0.11) (通过 package/System.Net.Security 4.0.0), System.Net.Primitives (>= 4.3.0) (通过 package/System.Net.NameResolution 4.3.0), System.Net.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Net.Primitives", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Reflection.Extensions”的互相冲突的请求: System.Reflection.Extensions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Reflection.Extensions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Reflection.Extensions (>= 4.0.1) (通过 package/System.Linq.Queryable 4.0.1), System.Reflection.Extensions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Reflection.Extensions (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0), System.Reflection.Extensions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Reflection.Extensions (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Reflection.Extensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Extensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Extensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Extensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Extensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Extensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.Extensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Reflection.Extensions", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Runtime.Handles”的互相冲突的请求: System.Runtime.Handles (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime.Handles (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime.Handles (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime.Handles (>= 4.0.1) (通过 package/System.Diagnostics.Process 4.1.0), System.Runtime.Handles (>= 4.0.1) (通过 package/System.Net.NameResolution 4.0.0), System.Runtime.Handles (>= 4.0.1) (通过 package/System.Net.Security 4.0.0), System.Runtime.Handles (>= 4.0.1) (通过 package/System.Security.SecureString 4.0.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Net.NameResolution 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.SecureString 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.Runtime.Handles (>= 4.0.1) (通过 package/System.Runtime.InteropServices 4.1.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Text.Encoding.CodePages 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.IO.FileSystem 4.3.0), System.Runtime.Handles (>= 4.3.0) (通过 package/System.Runtime.InteropServices 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Runtime.Handles", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Runtime.InteropServices”的互相冲突的请求: System.Runtime.InteropServices (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime.InteropServices (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime.InteropServices (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime.InteropServices (>= 4.1.0) (通过 package/System.Diagnostics.Process 4.1.0), System.Runtime.InteropServices (>= 4.1.0) (通过 package/System.Net.NameResolution 4.0.0), System.Runtime.InteropServices (>= 4.1.0) (通过 package/System.Net.Security 4.0.0), System.Runtime.InteropServices (>= 4.1.0) (通过 package/System.Security.SecureString 4.0.0), System.Runtime.InteropServices (>= 4.3.0) (通过 package/System.Net.NameResolution 4.3.0), System.Runtime.InteropServices (>= 4.3.0) (通过 package/System.Security.SecureString 4.3.0), System.Runtime.InteropServices (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Runtime.InteropServices (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime.InteropServices (>= 4.1.0) (通过 package/System.Globalization.Extensions 4.0.1), System.Runtime.InteropServices (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Runtime.InteropServices (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime.InteropServices (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime.InteropServices (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime.InteropServices (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime.InteropServices (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime.InteropServices (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Runtime.InteropServices (>= 4.3.0) (通过 package/System.Globalization.Extensions 4.3.0), System.Runtime.InteropServices (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Runtime.InteropServices", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Security.Cryptography.Primitives”的互相冲突的请求: System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Security.Cryptography.Primitives (>= 4.0.0) (通过 package/System.Net.Security 4.0.0), System.Security.Cryptography.Primitives (>= 4.0.0) (通过 package/System.Security.SecureString 4.0.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.SecureString 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Algorithms 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.Encoding 4.3.0), System.Security.Cryptography.Primitives (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Security.Cryptography.Primitives", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Text.Encoding”的互相冲突的请求: System.Text.Encoding (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Text.Encoding (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.0.11) (通过 package/System.Diagnostics.Process 4.1.0), System.Text.Encoding (>= 4.0.11) (通过 package/System.Net.Security 4.0.0), System.Text.Encoding (>= 4.0.11) (通过 package/System.Security.SecureString 4.0.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.Security.SecureString 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Text.Encoding (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Text.Encoding (>= 4.0.11) (通过 package/System.Security.Principal.Windows 4.0.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Text.Encoding (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Text.Encoding (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Text.Encoding (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Text.Encoding (>= 4.0.11) (通过 package/System.IO 4.1.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.3.0) (通过 package/System.IO 4.3.0), System.Text.Encoding (>= 4.0.11) (通过 package/System.IO 4.1.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Text.Encoding", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Text.Encoding.Extensions”的互相冲突的请求: System.Text.Encoding.Extensions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Text.Encoding.Extensions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Text.Encoding.Extensions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Text.Encoding.Extensions (>= 4.0.11) (通过 package/System.Diagnostics.Process 4.1.0), System.Text.Encoding.Extensions (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Text.Encoding.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding.Extensions (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Text.Encoding.Extensions (>= 4.3.0) (通过 package/System.Xml.ReaderWriter 4.3.0), System.Text.Encoding.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Text.Encoding.Extensions (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Text.Encoding.Extensions", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Threading”的互相冲突的请求: System.Threading (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Threading (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Threading (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Threading (>= 4.0.11) (通过 package/System.Collections.NonGeneric 4.0.1), System.Threading (>= 4.0.11) (通过 package/System.Diagnostics.Process 4.1.0), System.Threading (>= 4.0.11) (通过 package/System.Collections.Specialized 4.0.1), System.Threading (>= 4.0.11) (通过 package/System.Diagnostics.TraceSource 4.0.0), System.Threading (>= 4.0.11) (通过 package/System.Net.NameResolution 4.0.0), System.Threading (>= 4.0.11) (通过 package/System.Net.Security 4.0.0), System.Threading (>= 4.0.11) (通过 package/System.Security.SecureString 4.0.0), System.Threading (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0), System.Threading (>= 4.3.0) (通过 package/System.Net.NameResolution 4.3.0), System.Threading (>= 4.3.0) (通过 package/System.Security.SecureString 4.3.0), System.Threading (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.Threading (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Threading (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Threading (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Threading (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Threading (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Threading (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Threading (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Threading (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Threading (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Threading", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Xml.ReaderWriter”的互相冲突的请求: System.Xml.ReaderWriter (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Xml.ReaderWriter (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Xml.ReaderWriter (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Xml.ReaderWriter (>= 4.3.0) (通过 package/System.Xml.XDocument 4.3.0), System.Xml.ReaderWriter (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Xml.ReaderWriter (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.ReaderWriter (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Xml.ReaderWriter (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.ReaderWriter (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.ReaderWriter (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.ReaderWriter (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.ReaderWriter (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.ReaderWriter (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Xml.ReaderWriter", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Xml.XDocument”的互相冲突的请求: System.Xml.XDocument (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Xml.XDocument (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Xml.XDocument (>= 4.3.0) (通过 package/Microsoft.Identity.Client 4.14.0), System.Xml.XDocument (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Xml.XDocument (>= 4.3.0) (通过 package/NETStandard.Library 1.6.1), System.Xml.XDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.XDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.XDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.XDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.XDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.XDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.XDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Xml.XDocument", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Drawing.Common”的互相冲突的请求: System.Drawing.Common (>= 4.5.0) (通过 package/DotNetCore.NPOI.Core 1.2.1), System.Drawing.Common (>= 4.7.0) (通过 package/System.Windows.Extensions 4.7.0), System.Drawing.Common (>= 4.7.0) (通过 package/System.Windows.Extensions 4.7.0), System.Drawing.Common (>= 4.7.0) (通过 package/System.Windows.Extensions 4.7.0), System.Drawing.Common (>= 4.7.0) (通过 package/System.Windows.Extensions 4.7.0), System.Drawing.Common (>= 6.0.0) (通过 package/System.Windows.Extensions 6.0.0), System.Drawing.Common (>= 4.7.0) (通过 package/System.Windows.Extensions 4.7.0), System.Drawing.Common (>= 4.7.0) (通过 package/System.Windows.Extensions 4.7.0), System.Drawing.Common (>= 6.0.0) (通过 package/System.Windows.Extensions 6.0.0), System.Drawing.Common (>= 4.7.0) (通过 package/System.Windows.Extensions 4.7.0), System.Drawing.Common (>= 4.7.0) (通过 package/System.Windows.Extensions 4.7.0), System.Drawing.Common (>= 4.7.0) (通过 package/System.Windows.Extensions 4.7.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Drawing.Common", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.IdentityModel.Logging”的互相冲突的请求: Microsoft.IdentityModel.Logging (>= 6.6.0) (通过 package/Microsoft.IdentityModel.Tokens 6.6.0), Microsoft.IdentityModel.Logging (>= 6.8.0) (通过 package/Microsoft.IdentityModel.Protocols 6.8.0), Microsoft.IdentityModel.Logging (>= 6.8.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), Microsoft.IdentityModel.Logging (>= 6.8.0) (通过 package/Microsoft.IdentityModel.Protocols 6.8.0), Microsoft.IdentityModel.Logging (>= 6.8.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), Microsoft.IdentityModel.Logging (>= 5.6.0) (通过 package/Microsoft.IdentityModel.Protocols 5.6.0), Microsoft.IdentityModel.Logging (>= 5.6.0) (通过 package/Microsoft.IdentityModel.Tokens 5.6.0), Microsoft.IdentityModel.Logging (>= 6.8.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), Microsoft.IdentityModel.Logging (>= 6.8.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), Microsoft.IdentityModel.Logging (>= 5.6.0) (通过 package/Microsoft.IdentityModel.Tokens 5.6.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.IdentityModel.Logging", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Security.Cryptography.Cng”的互相冲突的请求: System.Security.Cryptography.Cng (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.6.0), System.Security.Cryptography.Cng (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Cng (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Cng (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), System.Security.Cryptography.Cng (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), System.Security.Cryptography.Cng (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 5.6.0), System.Security.Cryptography.Cng (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), System.Security.Cryptography.Cng (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), System.Security.Cryptography.Cng (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Cng (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), System.Security.Cryptography.Cng (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 6.8.0), System.Security.Cryptography.Cng (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 5.6.0), System.Security.Cryptography.Cng (>= 4.5.0) (通过 package/Microsoft.IdentityModel.Tokens 5.6.0), System.Security.Cryptography.Cng (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Cng (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Cng (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Cng (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Cng (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Cng (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Cng (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Cng (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), System.Security.Cryptography.Cng (>= 4.5.0) (通过 package/System.Security.Cryptography.Pkcs 4.5.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Security.Cryptography.Cng", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.NETCore.Targets”的互相冲突的请求: Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/Microsoft.Win32.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization.Calendars 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Net.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Net.Sockets 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Timer 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/Microsoft.Win32.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization.Calendars 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Net.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Net.Sockets 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Timer 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Collections 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Diagnostics.Debug 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Reflection 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Reflection.Extensions 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Resources.ResourceManager 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime 4.1.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.IO.Compression 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.3) (通过 package/System.Private.Uri 4.3.2), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.IO.Compression 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/Microsoft.Win32.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization.Calendars 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Net.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Net.Sockets 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Timer 4.3.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.Extensions 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Diagnostics.Debug 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Globalization 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Resources.ResourceManager 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.Extensions 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/Microsoft.Win32.Primitives 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/runtime.native.System 4.0.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Collections 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Diagnostics.Debug 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Globalization 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.IO 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.IO.FileSystem 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Resources.ResourceManager 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.Extensions 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.Handles 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.InteropServices 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Text.Encoding 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Text.Encoding.Extensions 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Threading.Tasks 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Reflection 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Reflection.Primitives 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Globalization 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Resources.ResourceManager 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.Extensions 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/runtime.native.System 4.0.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Collections 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Diagnostics.Debug 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Globalization 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Resources.ResourceManager 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.Extensions 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/runtime.native.System 4.0.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Collections 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Diagnostics.Tracing 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Globalization 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Net.Primitives 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Resources.ResourceManager 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.Extensions 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.Handles 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.InteropServices 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Threading.Tasks 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/Microsoft.Win32.Primitives 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/runtime.native.System 4.0.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/runtime.native.System.Net.Security 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/runtime.native.System.Security.Cryptography 4.0.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Collections 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Diagnostics.Tracing 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Globalization 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.IO 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Net.Primitives 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Resources.ResourceManager 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.Extensions 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.Handles 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.InteropServices 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Text.Encoding 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Threading.Tasks 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Resources.ResourceManager 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.Handles 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.InteropServices 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Text.Encoding 4.0.11), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Net.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/Microsoft.Win32.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization.Calendars 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Net.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Net.Sockets 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Timer 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.IO.Compression 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Threading.Tasks 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Diagnostics.Debug 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Runtime.InteropServices 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Threading.Tasks 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Threading.Tasks 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/Microsoft.Win32.Primitives 4.0.1), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Diagnostics.Debug 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Reflection 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Text.Encoding 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Diagnostics.Debug 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Reflection 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Diagnostics.Debug 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Diagnostics.Debug 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Globalization 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.IO 4.1.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Threading.Tasks 4.0.11), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.Threading.Tasks 4.0.11), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Collections 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Console 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Debug 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tools 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Resources.ResourceManager 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Text.Encoding.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Threading.Tasks 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.IO.Compression 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.IO.Compression 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization.Calendars 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.0.1) (通过 package/System.IO 4.1.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO.FileSystem 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.InteropServices 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.IO.Compression 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization.Calendars 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.IO.Compression 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization.Calendars 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.IO.Compression 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization.Calendars 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.IO.Compression 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization.Calendars 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.IO.Compression 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization.Calendars 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.IO.Compression 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Extensions 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Reflection.Primitives 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/runtime.native.System.Net.Http 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Globalization.Calendars 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Runtime.Handles 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.Diagnostics.Tracing 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0), Microsoft.NETCore.Targets (>= 1.1.0) (通过 package/System.IO 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.NETCore.Targets", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“SQLitePCLRaw.core”的互相冲突的请求: SQLitePCLRaw.core (>= 2.0.4) (通过 package/Microsoft.Data.Sqlite.Core 5.0.5), SQLitePCLRaw.core (>= 2.0.4) (通过 package/SQLitePCLRaw.bundle_e_sqlite3 2.0.4), SQLitePCLRaw.core (>= 2.0.4) (通过 package/Microsoft.Data.Sqlite.Core 5.0.5), SQLitePCLRaw.core (>= 2.0.4) (通过 package/SQLitePCLRaw.bundle_e_sqlite3 2.0.4) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "SQLitePCLRaw.core", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“SQLitePCLRaw.provider.dynamic_cdecl”的互相冲突的请求: SQLitePCLRaw.provider.dynamic_cdecl (>= 2.0.4) (通过 package/SQLitePCLRaw.bundle_e_sqlite3 2.0.4), SQLitePCLRaw.provider.dynamic_cdecl (>= 2.0.4) (通过 package/SQLitePCLRaw.bundle_e_sqlite3 2.0.4) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "SQLitePCLRaw.provider.dynamic_cdecl", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“SQLitePCLRaw.lib.e_sqlite3”的互相冲突的请求: SQLitePCLRaw.lib.e_sqlite3 (>= 2.0.4) (通过 package/SQLitePCLRaw.bundle_e_sqlite3 2.0.4), SQLitePCLRaw.lib.e_sqlite3 (>= 2.0.4) (通过 package/SQLitePCLRaw.bundle_e_sqlite3 2.0.4) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "SQLitePCLRaw.lib.e_sqlite3", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“K4os.Compression.LZ4”的互相冲突的请求: K4os.Compression.LZ4 (>= 1.2.6) (通过 package/K4os.Compression.LZ4.Streams 1.2.6), K4os.Compression.LZ4 (>= 1.2.6) (通过 package/K4os.Compression.LZ4.Streams 1.2.6) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "K4os.Compression.LZ4", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“K4os.Hash.xxHash”的互相冲突的请求: K4os.Hash.xxHash (>= 1.0.6) (通过 package/K4os.Compression.LZ4.Streams 1.2.6), K4os.Hash.xxHash (>= 1.0.6) (通过 package/K4os.Compression.LZ4.Streams 1.2.6) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "K4os.Hash.xxHash", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Security.Cryptography.ProtectedData”的互相冲突的请求: System.Security.Cryptography.ProtectedData (>= 4.4.0) (通过 package/System.Configuration.ConfigurationManager 4.4.1), System.Security.Cryptography.ProtectedData (>= 4.7.0) (通过 package/System.Configuration.ConfigurationManager 4.7.0), System.Security.Cryptography.ProtectedData (>= 6.0.0) (通过 package/System.Configuration.ConfigurationManager 6.0.0), System.Security.Cryptography.ProtectedData (>= 4.7.0) (通过 package/System.Configuration.ConfigurationManager 4.7.0), System.Security.Cryptography.ProtectedData (>= 4.4.0) (通过 package/System.Configuration.ConfigurationManager 4.4.1), System.Security.Cryptography.ProtectedData (>= 6.0.0) (通过 package/System.Configuration.ConfigurationManager 6.0.0), System.Security.Cryptography.ProtectedData (>= 4.7.0) (通过 package/System.Configuration.ConfigurationManager 4.7.0), System.Security.Cryptography.ProtectedData (>= 4.7.0) (通过 package/System.Configuration.ConfigurationManager 4.7.0), System.Security.Cryptography.ProtectedData (>= 4.7.0) (通过 package/System.Configuration.ConfigurationManager 4.7.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Security.Cryptography.ProtectedData", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Security.AccessControl”的互相冲突的请求: System.Security.AccessControl (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Security.AccessControl (>= 4.7.0) (通过 package/System.DirectoryServices 4.7.0), System.Security.AccessControl (>= 4.7.0) (通过 package/Microsoft.Win32.Registry 4.7.0), System.Security.AccessControl (>= 5.0.0) (通过 package/Microsoft.Win32.Registry 5.0.0), System.Security.AccessControl (>= 4.7.0) (通过 package/Microsoft.Win32.Registry 4.7.0), System.Security.AccessControl (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Security.AccessControl (>= 4.7.0) (通过 package/System.DirectoryServices 4.7.0), System.Security.AccessControl (>= 5.0.0) (通过 package/Microsoft.Win32.Registry 5.0.0), System.Security.AccessControl (>= 4.7.0) (通过 package/Microsoft.Win32.Registry 4.7.0), System.Security.AccessControl (>= 4.7.0) (通过 package/Microsoft.Win32.Registry 4.7.0), System.Security.AccessControl (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Security.AccessControl (>= 6.0.0) (通过 package/System.Security.Permissions 6.0.0), System.Security.AccessControl (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Security.AccessControl (>= 4.7.0) (通过 package/Microsoft.Win32.Registry 4.7.0), System.Security.AccessControl (>= 6.0.0) (通过 package/System.Security.Permissions 6.0.0), System.Security.AccessControl (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Security.AccessControl (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Security.AccessControl (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Security.AccessControl (>= 4.5.0) (通过 package/Microsoft.Win32.Registry 4.5.0), System.Security.AccessControl (>= 4.5.0) (通过 package/System.Security.Permissions 4.5.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Security.AccessControl", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Windows.Extensions”的互相冲突的请求: System.Windows.Extensions (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Windows.Extensions (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Windows.Extensions (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Windows.Extensions (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Windows.Extensions (>= 6.0.0) (通过 package/System.Security.Permissions 6.0.0), System.Windows.Extensions (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Windows.Extensions (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Windows.Extensions (>= 6.0.0) (通过 package/System.Security.Permissions 6.0.0), System.Windows.Extensions (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Windows.Extensions (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0), System.Windows.Extensions (>= 4.7.0) (通过 package/System.Security.Permissions 4.7.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Windows.Extensions", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.IO.FileSystem.AccessControl”的互相冲突的请求: System.IO.FileSystem.AccessControl (>= 4.7.0) (通过 package/System.DirectoryServices 4.7.0), System.IO.FileSystem.AccessControl (>= 4.7.0) (通过 package/System.DirectoryServices 4.7.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.IO.FileSystem.AccessControl", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.IdentityModel.Protocols”的互相冲突的请求: Microsoft.IdentityModel.Protocols (>= 6.8.0) (通过 package/Microsoft.IdentityModel.Protocols.OpenIdConnect 6.8.0), Microsoft.IdentityModel.Protocols (>= 6.8.0) (通过 package/Microsoft.IdentityModel.Protocols.OpenIdConnect 6.8.0), Microsoft.IdentityModel.Protocols (>= 5.6.0) (通过 package/Microsoft.IdentityModel.Protocols.OpenIdConnect 5.6.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.IdentityModel.Protocols", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Reflection.TypeExtensions”的互相冲突的请求: System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/Microsoft.Extensions.PlatformAbstractions 1.1.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Dynamic.Runtime 4.3.0), System.Reflection.TypeExtensions (>= 4.3.0) (通过 package/System.Linq.Expressions 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Reflection.TypeExtensions", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Collections.NonGeneric”的互相冲突的请求: System.Collections.NonGeneric (>= 4.0.1) (通过 package/MongoDB.Bson 2.4.4), System.Collections.NonGeneric (>= 4.0.1) (通过 package/System.Collections.Specialized 4.0.1), System.Collections.NonGeneric (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Collections.NonGeneric", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Collections.Specialized”的互相冲突的请求: System.Collections.Specialized (>= 4.0.1) (通过 package/MongoDB.Driver.Core 2.4.4), System.Collections.Specialized (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Collections.Specialized", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Net.NameResolution”的互相冲突的请求: System.Net.NameResolution (>= 4.0.0) (通过 package/MongoDB.Driver.Core 2.4.4), System.Net.NameResolution (>= 4.3.0) (通过 package/Microsoft.Identity.Client 4.14.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Net.NameResolution", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Security.SecureString”的互相冲突的请求: System.Security.SecureString (>= 4.0.0) (通过 package/MongoDB.Driver.Core 2.4.4), System.Security.SecureString (>= 4.3.0) (通过 package/Microsoft.Identity.Client 4.14.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Security.SecureString", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“runtime.native.System”的互相冲突的请求: runtime.native.System (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Net.Http 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Runtime.InteropServices.RuntimeInformation 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Net.Http 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Runtime.InteropServices.RuntimeInformation 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), runtime.native.System (>= 4.0.0) (通过 package/System.Diagnostics.Process 4.1.0), runtime.native.System (>= 4.0.0) (通过 package/System.Diagnostics.TraceSource 4.0.0), runtime.native.System (>= 4.0.0) (通过 package/System.Net.NameResolution 4.0.0), runtime.native.System (>= 4.0.0) (通过 package/System.Net.Security 4.0.0), runtime.native.System (>= 4.3.0) (通过 package/System.Net.NameResolution 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Net.Http 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Runtime.InteropServices.RuntimeInformation 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Net.Http 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Runtime.InteropServices.RuntimeInformation 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.IO.Compression 4.3.0), runtime.native.System (>= 4.3.0) (通过 package/System.Security.Cryptography.X509Certificates 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "runtime.native.System", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Globalization.Extensions”的互相冲突的请求: System.Globalization.Extensions (>= 4.3.0) (通过 package/System.Net.Http 4.3.0), System.Globalization.Extensions (>= 4.3.0) (通过 package/System.Net.Http 4.3.0), System.Globalization.Extensions (>= 4.0.1) (通过 package/System.Collections.Specialized 4.0.1), System.Globalization.Extensions (>= 4.0.1) (通过 package/System.Net.Security 4.0.0), System.Globalization.Extensions (>= 4.3.0) (通过 package/System.Net.Http 4.3.0), System.Globalization.Extensions (>= 4.3.0) (通过 package/System.Collections.Specialized 4.3.0), System.Globalization.Extensions (>= 4.3.0) (通过 package/System.Net.Http 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Globalization.Extensions", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.ComponentModel.TypeConverter”的互相冲突的请求: System.ComponentModel.TypeConverter (>= 4.3.0) (通过 package/Microsoft.Identity.Client 4.14.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.ComponentModel.TypeConverter", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Private.Uri”的互相冲突的请求: System.Private.Uri (>= 4.3.2) (通过 package/Microsoft.Identity.Client 4.14.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Private.Uri", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Runtime.Serialization.Formatters”的互相冲突的请求: System.Runtime.Serialization.Formatters (>= 4.3.0) (通过 package/Microsoft.Identity.Client 4.14.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Runtime.Serialization.Formatters", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Runtime.Serialization.Json”的互相冲突的请求: System.Runtime.Serialization.Json (>= 4.3.0) (通过 package/Microsoft.Identity.Client 4.14.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Runtime.Serialization.Json", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Runtime.Serialization.Primitives”的互相冲突的请求: System.Runtime.Serialization.Primitives (>= 4.3.0) (通过 package/Microsoft.Identity.Client 4.14.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Runtime.Serialization.Primitives", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“Microsoft.Win32.SystemEvents”的互相冲突的请求: Microsoft.Win32.SystemEvents (>= 4.5.0) (通过 package/System.Drawing.Common 4.5.0), Microsoft.Win32.SystemEvents (>= 4.7.0) (通过 package/System.Drawing.Common 4.7.0), Microsoft.Win32.SystemEvents (>= 4.7.0) (通过 package/System.Drawing.Common 4.7.0), Microsoft.Win32.SystemEvents (>= 4.7.0) (通过 package/System.Drawing.Common 4.7.0), Microsoft.Win32.SystemEvents (>= 4.7.0) (通过 package/System.Drawing.Common 4.7.0), Microsoft.Win32.SystemEvents (>= 6.0.0) (通过 package/System.Drawing.Common 6.0.0), Microsoft.Win32.SystemEvents (>= 4.7.0) (通过 package/System.Drawing.Common 4.7.0), Microsoft.Win32.SystemEvents (>= 4.7.0) (通过 package/System.Drawing.Common 4.7.0), Microsoft.Win32.SystemEvents (>= 6.0.0) (通过 package/System.Drawing.Common 6.0.0), Microsoft.Win32.SystemEvents (>= 4.7.0) (通过 package/System.Drawing.Common 4.7.0), Microsoft.Win32.SystemEvents (>= 4.7.0) (通过 package/System.Drawing.Common 4.7.0), Microsoft.Win32.SystemEvents (>= 4.7.0) (通过 package/System.Drawing.Common 4.7.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "Microsoft.Win32.SystemEvents", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Security.Claims”的互相冲突的请求: System.Security.Claims (>= 4.0.1) (通过 package/System.Net.Security 4.0.0), System.Security.Claims (>= 4.0.1) (通过 package/System.Security.Principal.Windows 4.0.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Security.Claims", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Security.Principal”的互相冲突的请求: System.Security.Principal (>= 4.0.1) (通过 package/System.Net.Security 4.0.0), System.Security.Principal (>= 4.0.1) (通过 package/System.Security.Principal.Windows 4.0.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Security.Principal", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.ComponentModel.Primitives”的互相冲突的请求: System.ComponentModel.Primitives (>= 4.3.0) (通过 package/System.ComponentModel.TypeConverter 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.ComponentModel.Primitives", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Private.DataContractSerialization”的互相冲突的请求: System.Private.DataContractSerialization (>= 4.3.0) (通过 package/System.Runtime.Serialization.Json 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Private.DataContractSerialization", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Xml.XmlDocument”的互相冲突的请求: System.Xml.XmlDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.XmlDocument (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0), System.Xml.XmlDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.XmlDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.XmlDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.XmlDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.XmlDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0), System.Xml.XmlDocument (>= 4.3.0) (通过 package/Microsoft.CodeAnalysis.Common 2.8.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Xml.XmlDocument", "targetGraphs": [".NETCoreApp,Version=v3.1"]}, {"code": "NU1106", "level": "Error", "message": "无法满足“System.Xml.XmlSerializer”的互相冲突的请求: System.Xml.XmlSerializer (>= 4.3.0) (通过 package/System.Private.DataContractSerialization 4.3.0) 框架: (.NETCoreApp,Version=v3.1)", "libraryId": "System.Xml.XmlSerializer", "targetGraphs": [".NETCoreApp,Version=v3.1"]}]}