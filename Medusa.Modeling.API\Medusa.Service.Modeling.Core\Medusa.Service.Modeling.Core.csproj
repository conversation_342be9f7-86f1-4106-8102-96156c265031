<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netcoreapp3.1</TargetFramework>
        <CodeAnalysisRuleSet>..\csharp.ruleset</CodeAnalysisRuleSet>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
        <DocumentationFile>bin\Debug\netcoreapp3.1\Medusa.Service.Modeling.Core.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
        <DocumentationFile>bin\Release\netcoreapp3.1\Medusa.Service.Modeling.Core.xml</DocumentationFile>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Medusa.Service.Cache" Version="1.0.19.8" />
        <PackageReference Include="MT.Enterprise.Core" Version="1.0.12.1" />
        <PackageReference Include="nacos-sdk-csharp-unofficial.AspNetCore" Version="0.8.5" />
        <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="MT.Enterprise.SDK" Version="1.1.8.2" />
        <PackageReference Include="MT.Enterprise.Utils" Version="1.0.13" />
        <PackageReference Include="MT.Enterprise.BPM.Reactor" Version="1.0.10" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Entities\Boost\" />
    </ItemGroup>

</Project>
