<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Medusa.Service.Modeling.Application</name>
    </assembly>
    <members>
        <member name="T:Medusa.Service.Modeling.Application.BusinessObjectManage.BusinessObjectManageService">
             业务对象管理接口实现
        </member>
        <member name="M:Medusa.Service.Modeling.Application.CRMReport.ICRMReportService.GetMarketProductCount(System.String)">
             市场-产品
        </member>
        <member name="T:Medusa.Service.Modeling.Application.CRMTasks.TaskTypeEnum">
             任务项配置枚举
        </member>
        <member name="P:Medusa.Service.Modeling.Application.PageModelingManage.Dtos.ViewSelectDto.Id">
             主键
        </member>
    </members>
</doc>
