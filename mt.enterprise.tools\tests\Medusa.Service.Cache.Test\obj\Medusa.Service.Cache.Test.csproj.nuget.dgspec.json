{"format": 1, "restore": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\tests\\Medusa.Service.Cache.Test\\Medusa.Service.Cache.Test.csproj": {}}, "projects": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\Medusa.Service.Cache\\Medusa.Service.Cache.csproj": {"version": "2.0.4.8", "restore": {"projectUniqueName": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\Medusa.Service.Cache\\Medusa.Service.Cache.csproj", "projectName": "Medusa.Service.Cache", "projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\Medusa.Service.Cache\\Medusa.Service.Cache.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\Medusa.Service.Cache\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\BPM\\BPM平台\\Pakages": {}, "D:\\项目\\Bpm_Chinahho\\BPM\\NugetPakage": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.Core\\MT.Enterprise.Core.csproj": {"projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.Core\\MT.Enterprise.Core.csproj"}, "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.Utils\\MT.Enterprise.Utils.csproj": {"projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.Utils\\MT.Enterprise.Utils.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.303\\RuntimeIdentifierGraph.json"}}}, "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.Core\\MT.Enterprise.Core.csproj": {"version": "1.0.14", "restore": {"projectUniqueName": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.Core\\MT.Enterprise.Core.csproj", "projectName": "MT.Enterprise.Core", "projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.Core\\MT.Enterprise.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\BPM\\BPM平台\\Pakages": {}, "D:\\项目\\Bpm_Chinahho\\BPM\\NugetPakage": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"AspectCore.Core": {"target": "Package", "version": "[2.2.0, )"}, "AspectCore.Extensions.DependencyInjection": {"target": "Package", "version": "[2.1.0, )"}, "MT.SqlSugar": {"target": "Package", "version": "[1.3.2, )"}, "Microsoft.AspNetCore.Hosting": {"target": "Package", "version": "[2.2.7, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.2.5, )"}, "Microsoft.Extensions.Caching.Abstractions": {"target": "Package", "version": "[3.1.7, )"}, "NLog": {"target": "Package", "version": "[5.0.5, )"}, "NLog.Database": {"target": "Package", "version": "[5.0.5, )"}, "NLog.Extensions.Logging": {"target": "Package", "version": "[5.1.0, )"}, "NLog.MongoDB.NetCore": {"target": "Package", "version": "[1.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.6.90, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[1.1.118, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.303\\RuntimeIdentifierGraph.json"}}}, "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.Utils\\MT.Enterprise.Utils.csproj": {"version": "1.0.13", "restore": {"projectUniqueName": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.Utils\\MT.Enterprise.Utils.csproj", "projectName": "MT.Enterprise.Utils", "projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.Utils\\MT.Enterprise.Utils.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\MT.Enterprise.Utils\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\BPM\\BPM平台\\Pakages": {}, "D:\\项目\\Bpm_Chinahho\\BPM\\NugetPakage": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "dependencies": {"AutoMapper": {"target": "Package", "version": "[9.0.0, )"}, "DotNetCore.NPOI": {"target": "Package", "version": "[1.2.1, )"}, "Microsoft.AspNetCore.Mvc": {"target": "Package", "version": "[2.2.0, )"}, "Mono.Cecil": {"target": "Package", "version": "[0.11.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[1.0.2, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[6.6.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.303\\RuntimeIdentifierGraph.json"}}}, "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\tests\\Medusa.Service.Cache.Test\\Medusa.Service.Cache.Test.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\tests\\Medusa.Service.Cache.Test\\Medusa.Service.Cache.Test.csproj", "projectName": "Medusa.Service.Cache.Test", "projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\tests\\Medusa.Service.Cache.Test\\Medusa.Service.Cache.Test.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\tests\\Medusa.Service.Cache.Test\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\BPM\\BPM平台\\Pakages": {}, "D:\\项目\\Bpm_Chinahho\\BPM\\NugetPakage": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\Medusa.Service.Cache\\Medusa.Service.Cache.csproj": {"projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\src\\Medusa.Service.Cache\\Medusa.Service.Cache.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"MSTest.TestAdapter": {"target": "Package", "version": "[2.2.7, )"}, "MSTest.TestFramework": {"target": "Package", "version": "[2.2.7, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[16.11.0, )"}, "coverlet.collector": {"target": "Package", "version": "[3.1.0, )"}, "xunit": {"target": "Package", "version": "[2.4.1, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.4.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.303\\RuntimeIdentifierGraph.json"}}}}}