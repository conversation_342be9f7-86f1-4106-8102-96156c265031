{"format": 1, "restore": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools.shuiwu\\src\\MT.Enterprise.Utils\\MT.Enterprise.Utils.csproj": {}}, "projects": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools.shuiwu\\src\\MT.Enterprise.Utils\\MT.Enterprise.Utils.csproj": {"version": "1.0.13", "restore": {"projectUniqueName": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools.shuiwu\\src\\MT.Enterprise.Utils\\MT.Enterprise.Utils.csproj", "projectName": "MT.Enterprise.Utils.ShuiWu", "projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools.shuiwu\\src\\MT.Enterprise.Utils\\MT.Enterprise.Utils.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools.shuiwu\\src\\MT.Enterprise.Utils\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\BPM\\BPM平台\\Pakages": {}, "D:\\项目\\Bpm_Chinahho\\BPM\\NugetPakage": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "dependencies": {"AutoMapper": {"target": "Package", "version": "[9.0.0, )"}, "DotNetCore.NPOI": {"target": "Package", "version": "[1.2.1, )"}, "Microsoft.AspNetCore.Mvc": {"target": "Package", "version": "[2.2.0, )"}, "Mono.Cecil": {"target": "Package", "version": "[0.11.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[1.0.2, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[6.6.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.303\\RuntimeIdentifierGraph.json"}}}}}