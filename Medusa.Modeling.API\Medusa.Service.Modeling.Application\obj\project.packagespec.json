﻿"restore":{"projectUniqueName":"E:\\03Work\\07Movitech\\03Project\\Movitech Platform\\Low Code Data Center\\Medusa.Modeling.API\\Medusa.Service.Modeling.Application\\Medusa.Service.Modeling.Application.csproj","projectName":"Medusa.Service.Modeling.Application","projectPath":"E:\\03Work\\07Movitech\\03Project\\Movitech Platform\\Low Code Data Center\\Medusa.Modeling.API\\Medusa.Service.Modeling.Application\\Medusa.Service.Modeling.Application.csproj","outputPath":"E:\\03Work\\07Movitech\\03Project\\Movitech Platform\\Low Code Data Center\\Medusa.Modeling.API\\Medusa.Service.Modeling.Application\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["netcoreapp3.1"],"sources":{"D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"http://172.19.50.115:15840/nuget":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"netcoreapp3.1":{"targetAlias":"netcoreapp3.1","projectReferences":{"E:\\03Work\\07Movitech\\03Project\\Movitech Platform\\Low Code Data Center\\Medusa.Modeling.API\\Medusa.Service.Modeling.Core\\Medusa.Service.Modeling.Core.csproj":{"projectPath":"E:\\03Work\\07Movitech\\03Project\\Movitech Platform\\Low Code Data Center\\Medusa.Modeling.API\\Medusa.Service.Modeling.Core\\Medusa.Service.Modeling.Core.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"netcoreapp3.1":{"targetAlias":"netcoreapp3.1","dependencies":{"Microsoft.Extensions.Configuration.Json":{"target":"Package","version":"[5.0.0, )"},"Microsoft.Extensions.Hosting.Abstractions":{"target":"Package","version":"[3.1.15, )"},"SmartFormat.NET":{"target":"Package","version":"[2.4.2, )"},"StyleCop.Analyzers":{"include":"Runtime, Build, Native, ContentFiles, Analyzers","suppressParent":"All","target":"Package","version":"[1.1.118, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\8.0.205\\RuntimeIdentifierGraph.json"}}