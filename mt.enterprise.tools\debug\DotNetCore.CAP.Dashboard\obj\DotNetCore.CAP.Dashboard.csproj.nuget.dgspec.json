{"format": 1, "restore": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\debug\\DotNetCore.CAP.Dashboard\\DotNetCore.CAP.Dashboard.csproj": {}}, "projects": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\debug\\DotNetCore.CAP.Dashboard\\DotNetCore.CAP.Dashboard.csproj": {"version": "3.1.2", "restore": {"projectUniqueName": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\debug\\DotNetCore.CAP.Dashboard\\DotNetCore.CAP.Dashboard.csproj", "projectName": "MT.DotNetCore.CAP.Dashboard", "projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\debug\\DotNetCore.CAP.Dashboard\\DotNetCore.CAP.Dashboard.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\debug\\DotNetCore.CAP.Dashboard\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\BPM\\BPM平台\\Pakages": {}, "D:\\项目\\Bpm_Chinahho\\BPM\\NugetPakage": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\debug\\DotNetCore.CAP\\DotNetCore.CAP.csproj": {"projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\debug\\DotNetCore.CAP\\DotNetCore.CAP.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Consul": {"target": "Package", "version": "[1.6.1.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[3.1.7, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[3.1.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.303\\RuntimeIdentifierGraph.json"}}}, "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\debug\\DotNetCore.CAP\\DotNetCore.CAP.csproj": {"version": "3.1.2", "restore": {"projectUniqueName": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\debug\\DotNetCore.CAP\\DotNetCore.CAP.csproj", "projectName": "MT.DotNetCore.CAP", "projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\debug\\DotNetCore.CAP\\DotNetCore.CAP.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools\\debug\\DotNetCore.CAP\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\BPM\\BPM平台\\Pakages": {}, "D:\\项目\\Bpm_Chinahho\\BPM\\NugetPakage": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"JetBrains.Annotations": {"target": "Package", "version": "[2022.1.0, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[3.1.7, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[3.1.7, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}, "System.Diagnostics.DiagnosticSource": {"target": "Package", "version": "[4.7.1, )"}, "System.Threading.Channels": {"target": "Package", "version": "[4.7.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.303\\RuntimeIdentifierGraph.json"}}}}}