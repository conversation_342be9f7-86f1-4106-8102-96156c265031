{"Logging": {"LogLevel": {"Default": "Debug", "System": "Information", "Microsoft": "Information"}}, "Persistence": {"DbType": "Mysql", "ConnectionString": "server=*************;Port=3306;Database=LowCode;uid=bpm;Pwd=****************"}, "Boost": {"DbType": "Mysql", "ConnectionString": "server=*************;Database=Boost;uid=bpm;Pwd=****************"}, "ApiGateway": {"Host": "*************", "Port": "32000", "AppKey": "799e6e124ad95e09b055ae8c8cc53d7f", "AppSecret": "a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0", "Timeout": "60"}, "ApiRequest": {"Host": "*************:6379", "DB": 8, "Password": "7Qx0YFWyUDsbjzBv", "Timeout": "30", "IsHttps": false}, "EncryptKeys": {"BPM": "58e13310360446198c1c596f32ad86c6"}, "PlatformSettings": {"ProductLicense": "/platform/v1/product-license/{id}"}, "ProcessSettings": {"BusinessObject": "/process/v1/manage/processes/{id}/business-object", "StartProcess": "/process/endpoints/business-data", "RecallProcess": "/process/endpoints/{procinstno}/recall", "CancelProcess": "/process/endpoints/{procinstno}/cancel", "StartUrl": "http://*************:2005/customer/start?bsid={bsid}&btid={btid}&boid={boid}", "ReStartUrl": "http://*************:2005/customer/start/{procinstno}?bsid={bsid}&btid={btid}&boid={boid}"}, "Redis": {"Host": "*************:6379", "DB": 0, "Password": "7Qx0YFWyUDsbjzBv"}, "Message": {"Url": "http://*************:32008/v1/messages/Wf_EOPNotifyMessage", "SystemCode": "crm"}, "RabbitMQ": {"HostName": "*************", "Port": 5672, "UserName": "bpm", "Password": "1nsc72AZBxNFLtw4", "InterfaceQueue": "modeling.connect"}, "BusinessOrg": {"SET": "F6C33F44-11FE-44EE-9971-35D860EF78C8.0_7A6C2B1F-B76D-4C87-A132-E91C296B82D5.30", "3C": "F6C33F44-11FE-44EE-9971-35D860EF78C8.0_C58FD84E-A710-4243-A5E5-BE555ECAD385.30", "Mopro": "F6C33F44-11FE-44EE-9971-35D860EF78C8.0_3E81322A-2109-431B-93EB-860C524A6727.30", "PV": "F6C33F44-11FE-44EE-9971-35D860EF78C8.0_25E17CC8-7A08-410B-8575-91BE5BFB6799.30"}}