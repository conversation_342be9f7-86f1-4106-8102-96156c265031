{"format": 1, "restore": {"E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Medusa.Modeling.API\\Medusa.Service.Modeling.Core\\Medusa.Service.Modeling.Core.csproj": {}}, "projects": {"E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Medusa.Modeling.API\\Medusa.Service.Modeling.Core\\Medusa.Service.Modeling.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Medusa.Modeling.API\\Medusa.Service.Modeling.Core\\Medusa.Service.Modeling.Core.csproj", "projectName": "Medusa.Service.Modeling.Core", "projectPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Medusa.Modeling.API\\Medusa.Service.Modeling.Core\\Medusa.Service.Modeling.Core.csproj", "packagesPath": "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\", "outputPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Medusa.Modeling.API\\Medusa.Service.Modeling.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://172.19.50.115:15840/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"MT.Enterprise.BPM.Reactor": {"target": "Package", "version": "[1.0.10, )"}, "MT.Enterprise.Core": {"target": "Package", "version": "[1.0.12.1, )"}, "MT.Enterprise.SDK": {"target": "Package", "version": "[1.1.8.2, )"}, "MT.Enterprise.Utils": {"target": "Package", "version": "[1.0.13, )"}, "Medusa.Service.Cache": {"target": "Package", "version": "[1.0.19.8, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[1.1.118, )"}, "nacos-sdk-csharp-unofficial.AspNetCore": {"target": "Package", "version": "[0.8.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}