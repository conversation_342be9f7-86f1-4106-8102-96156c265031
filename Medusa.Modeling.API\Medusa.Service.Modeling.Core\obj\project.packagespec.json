﻿"restore":{"projectUniqueName":"E:\\03Work\\07Movitech\\03Project\\Movitech Platform\\Low Code Data Center\\Medusa.Modeling.API\\Medusa.Service.Modeling.Core\\Medusa.Service.Modeling.Core.csproj","projectName":"Medusa.Service.Modeling.Core","projectPath":"E:\\03Work\\07Movitech\\03Project\\Movitech Platform\\Low Code Data Center\\Medusa.Modeling.API\\Medusa.Service.Modeling.Core\\Medusa.Service.Modeling.Core.csproj","outputPath":"E:\\03Work\\07Movitech\\03Project\\Movitech Platform\\Low Code Data Center\\Medusa.Modeling.API\\Medusa.Service.Modeling.Core\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["netcoreapp3.1"],"sources":{"D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"http://172.19.50.115:15840/nuget":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"netcoreapp3.1":{"targetAlias":"netcoreapp3.1","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"netcoreapp3.1":{"targetAlias":"netcoreapp3.1","dependencies":{"MT.Enterprise.BPM.Reactor":{"target":"Package","version":"[1.0.43, )"},"MT.Enterprise.Core":{"target":"Package","version":"[1.0.18.2, )"},"MT.Enterprise.SDK":{"target":"Package","version":"[2.0.6, )"},"MT.Enterprise.Utils":{"target":"Package","version":"[1.0.13.5, )"},"Medusa.Service.Cache":{"target":"Package","version":"[2.0.11, )"},"StyleCop.Analyzers":{"include":"Runtime, Build, Native, ContentFiles, Analyzers","suppressParent":"All","target":"Package","version":"[1.1.118, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\8.0.205\\RuntimeIdentifierGraph.json"}}