<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MT.Enterprise.Vision</name>
    </assembly>
    <members>
        <member name="M:MT.Enterprise.Vision.Expression.#ctor">
             默认构造函数，使用盟拓格式的表达式，且使用 LambdaParser 和 FastExpressionCompiler 组合作为引擎
        </member>
        <member name="M:MT.Enterprise.Vision.Expression.#ctor(MT.Enterprise.Vision.EngineType)">
             使用盟拓格式的表达式，且使用自定义类型引擎
        </member>
    </members>
</doc>
