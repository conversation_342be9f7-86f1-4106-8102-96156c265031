﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Medusa.Service.Cache</id>
    <version>*******</version>
    <authors>Medusa.Service.Cache</authors>
    <description>平台标准缓存包</description>
    <repository type="git" />
    <dependencies>
      <group targetFramework=".NETCoreApp3.1">
        <dependency id="MT.Enterprise.Core" version="1.0.14" exclude="Build,Analyzers" />
        <dependency id="MT.Enterprise.Utils" version="1.0.13" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\项目\Bpm_Chinahho\BPM-M\mt.enterprise.tools\src\Medusa.Service.Cache\bin\Release\netcoreapp3.1\Medusa.Service.Cache.dll" target="lib\netcoreapp3.1\Medusa.Service.Cache.dll" />
  </files>
</package>