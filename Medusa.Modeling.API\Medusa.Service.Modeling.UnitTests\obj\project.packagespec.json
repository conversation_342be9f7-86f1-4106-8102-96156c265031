﻿"restore":{"projectUniqueName":"E:\\03Work\\07Movitech\\03Project\\Movitech Platform\\Low Code Data Center\\Medusa.Modeling.API\\Medusa.Service.Modeling.UnitTests\\Medusa.Service.Modeling.UnitTests.csproj","projectName":"Medusa.Service.Modeling.UnitTests","projectPath":"E:\\03Work\\07Movitech\\03Project\\Movitech Platform\\Low Code Data Center\\Medusa.Modeling.API\\Medusa.Service.Modeling.UnitTests\\Medusa.Service.Modeling.UnitTests.csproj","outputPath":"E:\\03Work\\07Movitech\\03Project\\Movitech Platform\\Low Code Data Center\\Medusa.Modeling.API\\Medusa.Service.Modeling.UnitTests\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["netcoreapp3.1"],"sources":{"D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"http://172.19.50.115:15840/nuget":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"netcoreapp3.1":{"targetAlias":"netcoreapp3.1","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"netcoreapp3.1":{"targetAlias":"netcoreapp3.1","dependencies":{"Microsoft.NET.Test.Sdk":{"target":"Package","version":"[17.1.0, )"},"NUnit":{"target":"Package","version":"[3.13.3, )"},"NUnit.Analyzers":{"target":"Package","version":"[3.3.0, )"},"NUnit3TestAdapter":{"target":"Package","version":"[4.2.1, )"},"coverlet.collector":{"target":"Package","version":"[3.1.2, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\8.0.205\\RuntimeIdentifierGraph.json"}}