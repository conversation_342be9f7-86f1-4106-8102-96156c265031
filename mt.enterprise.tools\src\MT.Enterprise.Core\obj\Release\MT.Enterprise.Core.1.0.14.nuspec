﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>MT.Enterprise.Core</id>
    <version>1.0.14</version>
    <authors><PERSON></authors>
    <description>盟拓软件企业应用事业部通用组件 - 核心部分</description>
    <repository type="git" />
    <dependencies>
      <group targetFramework=".NETCoreApp3.1">
        <dependency id="AspectCore.Core" version="2.2.0" exclude="Build,Analyzers" />
        <dependency id="AspectCore.Extensions.DependencyInjection" version="2.1.0" exclude="Build,Analyzers" />
        <dependency id="MT.SqlSugar" version="1.3.2" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Hosting" version="2.2.7" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Mvc.Core" version="2.2.5" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Caching.Abstractions" version="3.1.7" exclude="Build,Analyzers" />
        <dependency id="NLog" version="5.0.5" exclude="Build,Analyzers" />
        <dependency id="NLog.Database" version="5.0.5" exclude="Build,Analyzers" />
        <dependency id="NLog.Extensions.Logging" version="5.1.0" exclude="Build,Analyzers" />
        <dependency id="NLog.MongoDB.NetCore" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="Newtonsoft.Json" version="13.0.2" exclude="Build,Analyzers" />
        <dependency id="StackExchange.Redis" version="2.6.90" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\项目\Bpm_Chinahho\BPM-M\mt.enterprise.tools\src\MT.Enterprise.Core\bin\Release\netcoreapp3.1\MT.Enterprise.Core.dll" target="lib\netcoreapp3.1\MT.Enterprise.Core.dll" />
    <file src="D:\项目\Bpm_Chinahho\BPM-M\mt.enterprise.tools\src\MT.Enterprise.Core\bin\Release\netcoreapp3.1\MT.Enterprise.Core.xml" target="lib\netcoreapp3.1\MT.Enterprise.Core.xml" />
  </files>
</package>