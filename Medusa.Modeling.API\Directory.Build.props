<Project>
  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors />
  </PropertyGroup>

  <PropertyGroup>
    <!-- Package versions -->
    <MicrosoftExtensionsVersion>3.1.32</MicrosoftExtensionsVersion>
    <MicrosoftAspNetCoreVersion>3.1.32</MicrosoftAspNetCoreVersion>
    <SystemSecurityCryptographyProtectedDataVersion>6.0.0</SystemSecurityCryptographyProtectedDataVersion>
    <SystemSecurityAccessControlVersion>6.0.0</SystemSecurityAccessControlVersion>
    <SystemWindowsExtensionsVersion>6.0.0</SystemWindowsExtensionsVersion>
    <SystemSecurityPermissionsVersion>6.0.0</SystemSecurityPermissionsVersion>
    <MicrosoftWin32SystemEventsVersion>6.0.0</MicrosoftWin32SystemEventsVersion>
    <MicrosoftWin32RegistryVersion>5.0.0</MicrosoftWin32RegistryVersion>
    <SystemDrawingCommonVersion>6.0.0</SystemDrawingCommonVersion>
    <SystemDirectoryServicesVersion>4.7.0</SystemDirectoryServicesVersion>
    <SystemIOFileSystemAccessControlVersion>4.7.0</SystemIOFileSystemAccessControlVersion>
    <MicrosoftIdentityModelProtocolsVersion>6.8.0</MicrosoftIdentityModelProtocolsVersion>
    <MicrosoftIdentityModelProtocolsOpenIdConnectVersion>6.8.0</MicrosoftIdentityModelProtocolsOpenIdConnectVersion>
    <SystemReflectionTypeExtensionsVersion>4.3.0</SystemReflectionTypeExtensionsVersion>
    <SystemCollectionsNonGenericVersion>4.3.0</SystemCollectionsNonGenericVersion>
    <SystemCollectionsSpecializedVersion>4.3.0</SystemCollectionsSpecializedVersion>
    <SystemNetNameResolutionVersion>4.3.0</SystemNetNameResolutionVersion>
    <SystemSecuritySecureStringVersion>4.3.0</SystemSecuritySecureStringVersion>
    <RuntimeNativeSystemVersion>4.3.0</RuntimeNativeSystemVersion>
    <SystemGlobalizationExtensionsVersion>4.3.0</SystemGlobalizationExtensionsVersion>
    <SystemComponentModelTypeConverterVersion>4.3.0</SystemComponentModelTypeConverterVersion>
    <SystemPrivateUriVersion>4.3.2</SystemPrivateUriVersion>
    <SystemRuntimeSerializationFormattersVersion>4.3.0</SystemRuntimeSerializationFormattersVersion>
    <SystemRuntimeSerializationJsonVersion>4.3.0</SystemRuntimeSerializationJsonVersion>
    <SystemRuntimeSerializationPrimitivesVersion>4.3.0</SystemRuntimeSerializationPrimitivesVersion>
    <SystemSecurityClaimsVersion>4.3.0</SystemSecurityClaimsVersion>
    <SystemSecurityPrincipalVersion>4.3.0</SystemSecurityPrincipalVersion>
    <SystemComponentModelPrimitivesVersion>4.3.0</SystemComponentModelPrimitivesVersion>
    <SystemPrivateDataContractSerializationVersion>4.3.0</SystemPrivateDataContractSerializationVersion>
    <SystemXmlXmlDocumentVersion>4.3.0</SystemXmlXmlDocumentVersion>
    <SystemXmlXmlSerializerVersion>4.3.0</SystemXmlXmlSerializerVersion>
    <SQLitePCLRawCoreVersion>2.0.4</SQLitePCLRawCoreVersion>
    <SQLitePCLRawProviderDynamicCdeclVersion>2.0.4</SQLitePCLRawProviderDynamicCdeclVersion>
    <SQLitePCLRawLibESqlite3Version>2.0.4</SQLitePCLRawLibESqlite3Version>
    <SQLitePCLRawBundleESqlite3Version>2.0.4</SQLitePCLRawBundleESqlite3Version>
    <K4osCompressionLZ4Version>1.2.6</K4osCompressionLZ4Version>
    <K4osCompressionLZ4StreamsVersion>1.2.6</K4osCompressionLZ4StreamsVersion>
    <K4osHashXxHashVersion>1.0.6</K4osHashXxHashVersion>
    <MicrosoftDataSqliteCoreVersion>5.0.5</MicrosoftDataSqliteCoreVersion>
    <SystemConfigurationConfigurationManagerVersion>6.0.0</SystemConfigurationConfigurationManagerVersion>
    <MongoDBBsonVersion>2.4.4</MongoDBBsonVersion>
    <MongoDBDriverCoreVersion>2.4.4</MongoDBDriverCoreVersion>
    <MicrosoftIdentityClientVersion>4.14.0</MicrosoftIdentityClientVersion>
    <SystemIOCompressionVersion>4.3.0</SystemIOCompressionVersion>
    <SystemNetHttpVersion>4.3.0</SystemNetHttpVersion>
    <SystemRuntimeInteropServicesRuntimeInformationVersion>4.3.0</SystemRuntimeInteropServicesRuntimeInformationVersion>
    <SystemSecurityCryptographyX509CertificatesVersion>4.3.0</SystemSecurityCryptographyX509CertificatesVersion>
    <SystemDiagnosticsProcessVersion>4.1.0</SystemDiagnosticsProcessVersion>
    <SystemDiagnosticsTraceSourceVersion>4.0.0</SystemDiagnosticsTraceSourceVersion>
    <SystemNetSecurityVersion>4.0.0</SystemNetSecurityVersion>
    <SystemSecurityPrincipalWindowsVersion>4.0.0</SystemSecurityPrincipalWindowsVersion>
    <MicrosoftExtensionsPlatformAbstractionsVersion>1.1.0</MicrosoftExtensionsPlatformAbstractionsVersion>
    <SystemLinqExpressionsVersion>4.3.0</SystemLinqExpressionsVersion>
    <SystemDynamicRuntimeVersion>4.3.0</SystemDynamicRuntimeVersion>
    <MicrosoftCodeAnalysisCommonVersion>2.8.0</MicrosoftCodeAnalysisCommonVersion>
  </PropertyGroup>
</Project>
