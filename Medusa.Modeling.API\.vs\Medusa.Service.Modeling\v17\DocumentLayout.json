{"Version": 1, "WorkspaceRootPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\Medusa.Modeling.API\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{057266AE-9B9B-4D64-ACB2-3DAAA20E9552}|Medusa.Service.Modeling.Application\\Medusa.Service.Modeling.Application.csproj|d:\\项目\\bpm_chinahho\\bpm-m\\medusa.modeling.api\\medusa.service.modeling.application\\processrelated\\processrelatedservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{057266AE-9B9B-4D64-ACB2-3DAAA20E9552}|Medusa.Service.Modeling.Application\\Medusa.Service.Modeling.Application.csproj|solutionrelative:medusa.service.modeling.application\\processrelated\\processrelatedservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "ProcessRelatedService.cs", "DocumentMoniker": "D:\\项目\\Bpm_Chinahho\\BPM-M\\Medusa.Modeling.API\\Medusa.Service.Modeling.Application\\ProcessRelated\\ProcessRelatedService.cs", "RelativeDocumentMoniker": "Medusa.Service.Modeling.Application\\ProcessRelated\\ProcessRelatedService.cs", "ToolTip": "D:\\项目\\Bpm_Chinahho\\BPM-M\\Medusa.Modeling.API\\Medusa.Service.Modeling.Application\\ProcessRelated\\ProcessRelatedService.cs", "RelativeToolTip": "Medusa.Service.Modeling.Application\\ProcessRelated\\ProcessRelatedService.cs", "ViewState": "AQIAABoAAAAAAAAAAAD4vx8AAAAFAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T08:23:45.58Z", "EditorCaption": ""}]}]}]}