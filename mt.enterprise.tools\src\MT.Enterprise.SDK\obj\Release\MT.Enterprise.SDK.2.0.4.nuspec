﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>MT.Enterprise.SDK</id>
    <version>2.0.4</version>
    <authors><PERSON></authors>
    <description>盟拓软件企业应用事业部通用组件 - SDK</description>
    <repository type="git" />
    <dependencies>
      <group targetFramework=".NETCoreApp3.1">
        <dependency id="DotNetCore.CAP" version="3.1.2" exclude="Build,Analyzers" />
        <dependency id="DotNetCore.CAP.Dashboard" version="3.1.2" exclude="Build,Analyzers" />
        <dependency id="DotNetCore.CAP.MySql" version="3.1.2" exclude="Build,Analyzers" />
        <dependency id="DotNetCore.CAP.RabbitMQ" version="3.1.2" exclude="Build,Analyzers" />
        <dependency id="DotNetCore.CAP.SqlServer" version="3.1.2" exclude="Build,Analyzers" />
        <dependency id="HttpClientFactory" version="1.0.3" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Http.Abstractions" version="2.2.0" exclude="Build,Analyzers" />
        <dependency id="Newtonsoft.Json" version="13.0.2" exclude="Build,Analyzers" />
        <dependency id="StackExchange.Redis" version="2.2.88" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\项目\Bpm_Chinahho\BPM-M\mt.enterprise.tools\src\MT.Enterprise.SDK\bin\Release\netcoreapp3.1\MT.Enterprise.SDK.dll" target="lib\netcoreapp3.1\MT.Enterprise.SDK.dll" />
    <file src="D:\项目\Bpm_Chinahho\BPM-M\mt.enterprise.tools\src\MT.Enterprise.SDK\bin\Release\netcoreapp3.1\MT.Enterprise.SDK.xml" target="lib\netcoreapp3.1\MT.Enterprise.SDK.xml" />
  </files>
</package>