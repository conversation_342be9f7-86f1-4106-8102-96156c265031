<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Medusa.Service.Modeling.Entrance</name>
    </assembly>
    <members>
        <member name="M:Medusa.Service.Modeling.Entrance.Controllers.V1_DynamicSqlController.Update(Newtonsoft.Json.Linq.JObject,System.Guid)">
             Update
        </member>
        <member name="M:Medusa.Service.Modeling.Entrance.Controllers.V1_SETReportController.GetProductType(System.String)">
             产品大类数据源
        </member>
        <member name="M:Medusa.Service.Modeling.Entrance.Controllers.V1_SETReportController.GetStageCountBySearch_SET(Medusa.Service.Modeling.Application.CRMReport.Dto.SearchDto)">
             产品大类数据源
        </member>
        <member name="M:Medusa.Service.Modeling.Entrance.Controllers.V1_SETReportController.GetMarketCount(System.String)">
             项目经理项目数量占比
        </member>
        <member name="M:Medusa.Service.Modeling.Entrance.Controllers.V1_SETReportController.GetSalesCount(System.String)">
             销售担当项目数量占比
        </member>
        <member name="M:Medusa.Service.Modeling.Entrance.Controllers.V1_SETReportController.GetUsers(System.String)">
             项目经理项目数量占比
        </member>
        <member name="M:Medusa.Service.Modeling.Entrance.Controllers.V1_SETReportController.GetSXTypeProjectCount(System.String)">
             项目经理项目数量占比
        </member>
        <member name="M:Medusa.Service.Modeling.Entrance.Controllers.V1_SETReportController.GetSXTypeCustomerCount(System.String)">
             项目经理项目数量占比
        </member>
        <member name="M:Medusa.Service.Modeling.Entrance.Controllers.V1_SETReportController.GetProvinceCount(System.String)">
             项目区域分布
        </member>
        <member name="M:Medusa.Service.Modeling.Entrance.Controllers.V1_SETReportController.GetSalesProductCount(System.String)">
             销售-产品
        </member>
        <member name="M:Medusa.Service.Modeling.Entrance.Controllers.V1_SETReportController.GetMarketProductCount(System.String)">
             市场-产品
        </member>
    </members>
</doc>
