{"version": 2, "dgSpecHash": "2Dxu31NIXx4=", "success": true, "projectFilePath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Medusa.Modeling.API\\Medusa.Service.Modeling.Connect\\Medusa.Service.Modeling.Connect.csproj", "expectedPackageFiles": ["D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\aspectcore.abstractions\\2.2.0\\aspectcore.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\aspectcore.core\\2.2.0\\aspectcore.core.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\aspectcore.extensions.dependencyinjection\\2.1.0\\aspectcore.extensions.dependencyinjection.2.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\aspectcore.extensions.reflection\\2.2.0\\aspectcore.extensions.reflection.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\automapper\\9.0.0\\automapper.9.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\consul\\1.6.1.1\\consul.1.6.1.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\dnsclient\\1.3.1\\dnsclient.1.3.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\dotnetcore.cap\\3.1.2\\dotnetcore.cap.3.1.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\dotnetcore.cap.dashboard\\3.1.2\\dotnetcore.cap.dashboard.3.1.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\dotnetcore.cap.mysql\\3.1.2\\dotnetcore.cap.mysql.3.1.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\dotnetcore.cap.rabbitmq\\3.1.2\\dotnetcore.cap.rabbitmq.3.1.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\dotnetcore.cap.sqlserver\\3.1.2\\dotnetcore.cap.sqlserver.3.1.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\dotnetcore.npoi\\1.2.1\\dotnetcore.npoi.1.2.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\dotnetcore.npoi.core\\1.2.1\\dotnetcore.npoi.core.1.2.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\dotnetcore.npoi.openxml4net\\1.2.1\\dotnetcore.npoi.openxml4net.1.2.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\dotnetcore.npoi.openxmlformats\\1.2.1\\dotnetcore.npoi.openxmlformats.1.2.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\easycaching.core\\0.8.9\\easycaching.core.0.8.9.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\easycaching.inmemory\\0.8.9\\easycaching.inmemory.0.8.9.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\fastexpressioncompiler\\1.10.1\\fastexpressioncompiler.1.10.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\flee\\1.2.1\\flee.1.2.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\google.protobuf\\3.19.4\\google.protobuf.3.19.4.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\httpclientfactory\\1.0.3\\httpclientfactory.1.0.3.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\k4os.compression.lz4\\1.2.6\\k4os.compression.lz4.1.2.6.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\k4os.compression.lz4.streams\\1.2.6\\k4os.compression.lz4.streams.1.2.6.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\k4os.hash.xxhash\\1.0.6\\k4os.hash.xxhash.1.0.6.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\medusa.service.cache\\1.0.19.8\\medusa.service.cache.1.0.19.8.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.antiforgery\\2.2.0\\microsoft.aspnetcore.antiforgery.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.authentication.abstractions\\2.2.0\\microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.authentication.core\\2.2.0\\microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.authorization\\2.2.0\\microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.authorization.policy\\2.2.0\\microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.cors\\2.2.0\\microsoft.aspnetcore.cors.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.cryptography.internal\\2.2.0\\microsoft.aspnetcore.cryptography.internal.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.dataprotection\\2.2.0\\microsoft.aspnetcore.dataprotection.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.dataprotection.abstractions\\2.2.0\\microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.diagnostics.abstractions\\2.2.0\\microsoft.aspnetcore.diagnostics.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.hosting\\2.2.7\\microsoft.aspnetcore.hosting.2.2.7.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.hosting.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.hosting.server.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.html.abstractions\\2.2.0\\microsoft.aspnetcore.html.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.http\\2.2.0\\microsoft.aspnetcore.http.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.http.extensions\\2.2.0\\microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.jsonpatch\\2.2.0\\microsoft.aspnetcore.jsonpatch.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.localization\\2.2.0\\microsoft.aspnetcore.localization.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc\\2.2.0\\microsoft.aspnetcore.mvc.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc.abstractions\\2.2.0\\microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc.analyzers\\2.2.0\\microsoft.aspnetcore.mvc.analyzers.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc.apiexplorer\\2.2.0\\microsoft.aspnetcore.mvc.apiexplorer.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc.core\\2.2.5\\microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc.cors\\2.2.0\\microsoft.aspnetcore.mvc.cors.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc.dataannotations\\2.2.0\\microsoft.aspnetcore.mvc.dataannotations.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc.formatters.json\\2.2.0\\microsoft.aspnetcore.mvc.formatters.json.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc.localization\\2.2.0\\microsoft.aspnetcore.mvc.localization.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc.razor\\2.2.0\\microsoft.aspnetcore.mvc.razor.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc.razor.extensions\\2.2.0\\microsoft.aspnetcore.mvc.razor.extensions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc.razorpages\\2.2.0\\microsoft.aspnetcore.mvc.razorpages.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc.taghelpers\\2.2.0\\microsoft.aspnetcore.mvc.taghelpers.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.mvc.viewfeatures\\2.2.0\\microsoft.aspnetcore.mvc.viewfeatures.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.razor\\2.2.0\\microsoft.aspnetcore.razor.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.razor.design\\2.2.0\\microsoft.aspnetcore.razor.design.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.razor.language\\2.2.0\\microsoft.aspnetcore.razor.language.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.razor.runtime\\2.2.0\\microsoft.aspnetcore.razor.runtime.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.responsecaching.abstractions\\2.2.0\\microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.routing\\2.2.0\\microsoft.aspnetcore.routing.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.routing.abstractions\\2.2.0\\microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.webutilities\\2.2.0\\microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.bcl.asyncinterfaces\\1.1.1\\microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.bcl.hashcode\\1.1.0\\microsoft.bcl.hashcode.1.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.codeanalysis.analyzers\\1.1.0\\microsoft.codeanalysis.analyzers.1.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.codeanalysis.common\\2.8.0\\microsoft.codeanalysis.common.2.8.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.codeanalysis.csharp\\2.8.0\\microsoft.codeanalysis.csharp.2.8.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.codeanalysis.razor\\2.2.0\\microsoft.codeanalysis.razor.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.data.sqlclient\\2.1.1\\microsoft.data.sqlclient.2.1.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.data.sqlclient.sni.runtime\\2.1.1\\microsoft.data.sqlclient.sni.runtime.2.1.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.data.sqlite\\5.0.5\\microsoft.data.sqlite.5.0.5.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.data.sqlite.core\\5.0.5\\microsoft.data.sqlite.core.5.0.5.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.dotnet.platformabstractions\\2.1.0\\microsoft.dotnet.platformabstractions.2.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.entityframeworkcore\\3.1.7\\microsoft.entityframeworkcore.3.1.7.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.entityframeworkcore.abstractions\\3.1.7\\microsoft.entityframeworkcore.abstractions.3.1.7.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.entityframeworkcore.analyzers\\3.1.7\\microsoft.entityframeworkcore.analyzers.3.1.7.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.entityframeworkcore.relational\\3.1.7\\microsoft.entityframeworkcore.relational.3.1.7.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.caching.abstractions\\3.1.7\\microsoft.extensions.caching.abstractions.3.1.7.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.caching.memory\\3.1.7\\microsoft.extensions.caching.memory.3.1.7.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration\\5.0.0\\microsoft.extensions.configuration.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration.abstractions\\5.0.0\\microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration.binder\\3.1.7\\microsoft.extensions.configuration.binder.3.1.7.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration.environmentvariables\\2.2.4\\microsoft.extensions.configuration.environmentvariables.2.2.4.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration.fileextensions\\5.0.0\\microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration.json\\5.0.0\\microsoft.extensions.configuration.json.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.dependencyinjection\\3.1.7\\microsoft.extensions.dependencyinjection.3.1.7.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.dependencyinjection.abstractions\\3.1.15\\microsoft.extensions.dependencyinjection.abstractions.3.1.15.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.dependencymodel\\2.1.0\\microsoft.extensions.dependencymodel.2.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.fileproviders.abstractions\\5.0.0\\microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.fileproviders.composite\\2.2.0\\microsoft.extensions.fileproviders.composite.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.fileproviders.physical\\5.0.0\\microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.filesystemglobbing\\5.0.0\\microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.hosting.abstractions\\3.1.15\\microsoft.extensions.hosting.abstractions.3.1.15.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.http\\3.1.0\\microsoft.extensions.http.3.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.localization\\2.2.0\\microsoft.extensions.localization.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.localization.abstractions\\2.2.0\\microsoft.extensions.localization.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.logging\\3.1.7\\microsoft.extensions.logging.3.1.7.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.logging.abstractions\\3.1.15\\microsoft.extensions.logging.abstractions.3.1.15.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.objectpool\\2.2.0\\microsoft.extensions.objectpool.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.options\\3.1.7\\microsoft.extensions.options.3.1.7.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.options.configurationextensions\\3.1.0\\microsoft.extensions.options.configurationextensions.3.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.primitives\\5.0.0\\microsoft.extensions.primitives.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.webencoders\\2.2.0\\microsoft.extensions.webencoders.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.identity.client\\4.21.1\\microsoft.identity.client.4.21.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.identitymodel.jsonwebtokens\\6.8.0\\microsoft.identitymodel.jsonwebtokens.6.8.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.identitymodel.logging\\6.8.0\\microsoft.identitymodel.logging.6.8.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.identitymodel.protocols\\6.8.0\\microsoft.identitymodel.protocols.6.8.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.identitymodel.protocols.openidconnect\\6.8.0\\microsoft.identitymodel.protocols.openidconnect.6.8.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.identitymodel.tokens\\6.8.0\\microsoft.identitymodel.tokens.6.8.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.net.http.headers\\2.2.0\\microsoft.net.http.headers.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.netcore.platforms\\3.1.1\\microsoft.netcore.platforms.3.1.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mongodb.bson\\2.10.4\\mongodb.bson.2.10.4.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mongodb.driver\\2.10.4\\mongodb.driver.2.10.4.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mongodb.driver.core\\2.10.4\\mongodb.driver.core.2.10.4.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mongodb.libmongocrypt\\1.0.0\\mongodb.libmongocrypt.1.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mono.cecil\\0.11.3\\mono.cecil.0.11.3.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mt.enterprise.bpm.reactor\\1.0.10\\mt.enterprise.bpm.reactor.1.0.10.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mt.enterprise.core\\1.0.12.1\\mt.enterprise.core.1.0.12.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mt.enterprise.sdk\\1.1.8.2\\mt.enterprise.sdk.1.1.8.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mt.enterprise.utils\\1.0.13\\mt.enterprise.utils.1.0.13.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mt.enterprise.vision\\0.0.3\\mt.enterprise.vision.0.0.3.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mt.sqlsugar\\1.3.0.1\\mt.sqlsugar.1.3.0.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mysql.data\\8.0.31\\mysql.data.8.0.31.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mysqlconnector\\1.0.1\\mysqlconnector.1.0.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\nacos-sdk-csharp-unofficial\\0.8.5\\nacos-sdk-csharp-unofficial.0.8.5.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\nacos-sdk-csharp-unofficial.aspnetcore\\0.8.5\\nacos-sdk-csharp-unofficial.aspnetcore.0.8.5.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\netstandard.library\\1.6.1\\netstandard.library.1.6.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\newtonsoft.json.bson\\1.0.1\\newtonsoft.json.bson.1.0.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\nlog\\4.7.4\\nlog.4.7.4.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\nlog.extensions.logging\\1.6.5\\nlog.extensions.logging.1.6.5.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\nlog.mongo\\*********\\nlog.mongo.*********.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\npgsql\\5.0.7\\npgsql.5.0.7.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\oracle.manageddataaccess.core\\3.21.1\\oracle.manageddataaccess.core.3.21.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\oscar.data.sqlclient\\4.0.4\\oscar.data.sqlclient.4.0.4.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\pipelines.sockets.unofficial\\2.2.0\\pipelines.sockets.unofficial.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\portable.bouncycastle\\1.9.0\\portable.bouncycastle.1.9.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\rabbitmq.client\\6.2.1\\rabbitmq.client.6.2.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system.net.security\\4.3.0\\runtime.native.system.net.security.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\sharpcompress\\0.23.0\\sharpcompress.0.23.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\sharpziplib\\1.0.0\\sharpziplib.1.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\smartformat.net\\2.4.2\\smartformat.net.2.4.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\sqlitepclraw.bundle_e_sqlite3\\2.0.4\\sqlitepclraw.bundle_e_sqlite3.2.0.4.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\sqlitepclraw.core\\2.0.4\\sqlitepclraw.core.2.0.4.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\sqlitepclraw.lib.e_sqlite3\\2.0.4\\sqlitepclraw.lib.e_sqlite3.2.0.4.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\sqlitepclraw.provider.dynamic_cdecl\\2.0.4\\sqlitepclraw.provider.dynamic_cdecl.2.0.4.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\sqlsugarcore.dm\\1.0.0\\sqlsugarcore.dm.1.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\sqlsugarcore.kdbndp\\1.0.0\\sqlsugarcore.kdbndp.1.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\stackexchange.redis\\2.2.88\\stackexchange.redis.2.2.88.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.appcontext\\4.3.0\\system.appcontext.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.collections.immutable\\1.7.1\\system.collections.immutable.1.7.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.collections.nongeneric\\4.0.1\\system.collections.nongeneric.4.0.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.collections.specialized\\4.0.1\\system.collections.specialized.4.0.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.componentmodel.annotations\\4.7.0\\system.componentmodel.annotations.4.7.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.componentmodel.primitives\\4.1.0\\system.componentmodel.primitives.4.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.componentmodel.typeconverter\\4.1.0\\system.componentmodel.typeconverter.4.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.configuration.configurationmanager\\6.0.0\\system.configuration.configurationmanager.6.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.data.common\\4.3.0\\system.data.common.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.data.oledb\\6.0.0\\system.data.oledb.6.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.diagnosticsource\\4.7.1\\system.diagnostics.diagnosticsource.4.7.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.fileversioninfo\\4.3.0\\system.diagnostics.fileversioninfo.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.performancecounter\\6.0.0\\system.diagnostics.performancecounter.6.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.process\\4.1.0\\system.diagnostics.process.4.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.stacktrace\\4.3.0\\system.diagnostics.stacktrace.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.textwritertracelistener\\4.0.0\\system.diagnostics.textwritertracelistener.4.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.tracesource\\4.3.0\\system.diagnostics.tracesource.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.directoryservices\\4.7.0\\system.directoryservices.4.7.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.directoryservices.protocols\\4.7.0\\system.directoryservices.protocols.4.7.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.dynamic.runtime\\4.3.0\\system.dynamic.runtime.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.identitymodel.tokens.jwt\\6.8.0\\system.identitymodel.tokens.jwt.6.8.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.compression.zipfile\\4.3.0\\system.io.compression.zipfile.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.filesystem.accesscontrol\\4.7.0\\system.io.filesystem.accesscontrol.4.7.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.pipelines\\5.0.0\\system.io.pipelines.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.linq.dynamic.core\\1.0.9\\system.linq.dynamic.core.1.0.9.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.linq.queryable\\4.0.1\\system.linq.queryable.4.0.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.net.http\\4.3.0\\system.net.http.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.net.nameresolution\\4.3.0\\system.net.nameresolution.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.net.security\\4.3.2\\system.net.security.4.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.net.sockets\\4.3.0\\system.net.sockets.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.emit\\4.7.0\\system.reflection.emit.4.7.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.emit.ilgeneration\\4.7.0\\system.reflection.emit.ilgeneration.4.7.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.metadata\\1.6.0\\system.reflection.metadata.1.6.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.caching\\4.7.0\\system.runtime.caching.4.7.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.compilerservices.unsafe\\5.0.0\\system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.serialization.formatters\\4.3.0\\system.runtime.serialization.formatters.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.serialization.primitives\\4.3.0\\system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.claims\\4.3.0\\system.security.claims.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.cng\\4.5.0\\system.security.cryptography.cng.4.5.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.pkcs\\4.5.0\\system.security.cryptography.pkcs.4.5.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.protecteddata\\6.0.0\\system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.xml\\4.5.0\\system.security.cryptography.xml.4.5.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.principal\\4.3.0\\system.security.principal.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.securestring\\4.0.0\\system.security.securestring.4.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.encoding.codepages\\4.7.1\\system.text.encoding.codepages.4.7.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.encodings.web\\4.5.0\\system.text.encodings.web.4.5.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.json\\5.0.0\\system.text.json.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.regularexpressions\\4.3.0\\system.text.regularexpressions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.channels\\4.7.1\\system.threading.channels.4.7.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.tasks.extensions\\4.5.1\\system.threading.tasks.extensions.4.5.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.tasks.parallel\\4.3.0\\system.threading.tasks.parallel.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.thread\\4.3.0\\system.threading.thread.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.threadpool\\4.3.0\\system.threading.threadpool.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.timer\\4.3.0\\system.threading.timer.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.valuetuple\\4.3.0\\system.valuetuple.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.xml.xmldocument\\4.3.0\\system.xml.xmldocument.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.xml.xpath\\4.3.0\\system.xml.xpath.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.xml.xpath.xdocument\\4.3.0\\system.xml.xpath.xdocument.4.3.0.nupkg.sha512"], "logs": []}