{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.1", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v3.1": {"Medusa.Service.Modeling.Connect/1.0.0": {"dependencies": {"Medusa.Service.Modeling.Application": "1.0.0", "Medusa.Service.Modeling.Core": "1.0.0"}, "runtime": {"Medusa.Service.Modeling.Connect.dll": {}}}, "AspectCore.Abstractions/2.2.0": {"runtime": {"lib/netstandard2.1/AspectCore.Abstractions.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "AspectCore.Core/2.2.0": {"dependencies": {"AspectCore.Abstractions": "2.2.0", "AspectCore.Extensions.Reflection": "2.2.0", "Microsoft.CSharp": "4.7.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0"}, "runtime": {"lib/netstandard2.1/AspectCore.Core.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "AspectCore.Extensions.DependencyInjection/2.1.0": {"dependencies": {"AspectCore.Core": "2.2.0", "Microsoft.Extensions.DependencyInjection": "3.1.7"}, "runtime": {"lib/netstandard2.1/AspectCore.Extensions.DependencyInjection.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "AspectCore.Extensions.Reflection/2.2.0": {"runtime": {"lib/netstandard2.1/AspectCore.Extensions.Reflection.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "AutoMapper/9.0.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/AutoMapper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Consul/*******": {"dependencies": {"Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Consul.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DnsClient/1.3.1": {"runtime": {"lib/netstandard2.1/DnsClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.CAP/3.1.2": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "3.1.15", "Microsoft.Extensions.Options": "3.1.7", "Newtonsoft.Json": "13.0.1", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Threading.Channels": "4.7.1"}, "runtime": {"lib/netstandard2.0/DotNetCore.CAP.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.CAP.Dashboard/3.1.2": {"dependencies": {"Consul": "*******", "DotNetCore.CAP": "3.1.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.15", "Microsoft.Extensions.Logging.Abstractions": "3.1.15"}, "runtime": {"lib/netcoreapp3.1/DotNetCore.CAP.Dashboard.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "resources": {"lib/netcoreapp3.1/zh/DotNetCore.CAP.Dashboard.resources.dll": {"locale": "zh"}}}, "DotNetCore.CAP.MySql/3.1.2": {"dependencies": {"DotNetCore.CAP": "3.1.2", "Microsoft.EntityFrameworkCore": "3.1.7", "Microsoft.EntityFrameworkCore.Relational": "3.1.7", "MySqlConnector": "1.0.1"}, "runtime": {"lib/netstandard2.1/DotNetCore.CAP.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.CAP.RabbitMQ/3.1.2": {"dependencies": {"DotNetCore.CAP": "3.1.2", "RabbitMQ.Client": "6.2.1"}, "runtime": {"lib/netstandard2.0/DotNetCore.CAP.RabbitMQ.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.CAP.SqlServer/3.1.2": {"dependencies": {"DotNetCore.CAP": "3.1.2", "Microsoft.Data.SqlClient": "2.1.1", "Microsoft.EntityFrameworkCore": "3.1.7", "Microsoft.EntityFrameworkCore.Relational": "3.1.7"}, "runtime": {"lib/netstandard2.0/DotNetCore.CAP.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.NPOI/1.2.1": {"dependencies": {"DotNetCore.NPOI.Core": "1.2.1", "DotNetCore.NPOI.OpenXml4Net": "1.2.1", "DotNetCore.NPOI.OpenXmlFormats": "1.2.1"}, "runtime": {"lib/netstandard2.0/NPOI.OOXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.NPOI.Core/1.2.1": {"dependencies": {"SharpZipLib": "1.0.0", "System.Drawing.Common": "6.0.0", "System.Text.Encoding.CodePages": "4.7.1"}, "runtime": {"lib/netstandard2.0/NPOI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.NPOI.OpenXml4Net/1.2.1": {"dependencies": {"DotNetCore.NPOI.Core": "1.2.1"}, "runtime": {"lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.NPOI.OpenXmlFormats/1.2.1": {"dependencies": {"DotNetCore.NPOI.OpenXml4Net": "1.2.1"}, "runtime": {"lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EasyCaching.Core/0.8.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "3.1.7", "Microsoft.Extensions.Logging": "3.1.7", "Microsoft.Extensions.Options.ConfigurationExtensions": "3.1.0", "System.Diagnostics.DiagnosticSource": "4.7.1"}, "runtime": {"lib/netstandard2.0/EasyCaching.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EasyCaching.InMemory/0.8.9": {"dependencies": {"EasyCaching.Core": "0.8.9"}, "runtime": {"lib/netstandard2.0/EasyCaching.InMemory.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FastExpressionCompiler/1.10.1": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Reflection.Emit.Lightweight": "4.3.0"}, "runtime": {"lib/netstandard1.3/FastExpressionCompiler.dll": {"assemblyVersion": "1.10.1.0", "fileVersion": "1.10.1.0"}}}, "Flee/1.2.1": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Emit.Lightweight": "4.3.0"}, "runtime": {"lib/netstandard20/Flee.NetStandard20.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Google.Protobuf/3.19.4": {"dependencies": {"System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.0/Google.Protobuf.dll": {"assemblyVersion": "3.19.4.0", "fileVersion": "3.19.4.0"}}}, "HttpClientFactory/1.0.3": {"runtime": {"lib/netstandard2.0/HttpClientFactory.dll": {"assemblyVersion": "1.0.3.0", "fileVersion": "1.0.3.0"}}}, "K4os.Compression.LZ4/1.2.6": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/K4os.Compression.LZ4.dll": {"assemblyVersion": "1.2.6.0", "fileVersion": "1.2.6.0"}}}, "K4os.Compression.LZ4.Streams/1.2.6": {"dependencies": {"K4os.Compression.LZ4": "1.2.6", "K4os.Hash.xxHash": "1.0.6"}, "runtime": {"lib/netstandard2.1/K4os.Compression.LZ4.Streams.dll": {"assemblyVersion": "1.2.6.0", "fileVersion": "1.2.6.0"}}}, "K4os.Hash.xxHash/1.0.6": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/K4os.Hash.xxHash.dll": {"assemblyVersion": "1.0.6.0", "fileVersion": "1.0.6.0"}}}, "Medusa.Service.Cache/********": {"dependencies": {"MT.Enterprise.Core": "********", "MT.Enterprise.Utils": "1.0.13"}, "runtime": {"lib/netcoreapp3.1/Medusa.Service.Cache.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.AspNetCore.Antiforgery/2.2.0": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.15", "Microsoft.Extensions.Options": "3.1.7"}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}}, "Microsoft.AspNetCore.Authorization/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "3.1.15", "Microsoft.Extensions.Options": "3.1.7"}}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0"}}, "Microsoft.AspNetCore.Cors/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.15", "Microsoft.Extensions.Logging.Abstractions": "3.1.15", "Microsoft.Extensions.Options": "3.1.7"}}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.2.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.15", "Microsoft.Extensions.Logging.Abstractions": "3.1.15", "Microsoft.Extensions.Options": "3.1.7", "Microsoft.Win32.Registry": "5.0.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.2.0": {}, "Microsoft.AspNetCore.Hosting/2.2.7": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.2.4", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.DependencyInjection": "3.1.7", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Hosting.Abstractions": "3.1.15", "Microsoft.Extensions.Logging": "3.1.7", "Microsoft.Extensions.Options": "3.1.7", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Reflection.Metadata": "1.6.0"}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "3.1.15"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"dependencies": {"System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.AspNetCore.Http/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "3.1.7", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.1"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.AspNetCore.JsonPatch/2.2.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Localization/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Localization.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.15", "Microsoft.Extensions.Options": "3.1.7"}}, "Microsoft.AspNetCore.Mvc/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Analyzers": "2.2.0", "Microsoft.AspNetCore.Mvc.ApiExplorer": "2.2.0", "Microsoft.AspNetCore.Mvc.Cors": "2.2.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.AspNetCore.Mvc.Localization": "2.2.0", "Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.RazorPages": "2.2.0", "Microsoft.AspNetCore.Mvc.TagHelpers": "2.2.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.2.0", "Microsoft.AspNetCore.Razor.Design": "2.2.0", "Microsoft.Extensions.Caching.Memory": "3.1.7", "Microsoft.Extensions.DependencyInjection": "3.1.7"}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.Analyzers/2.2.0": {}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.5"}}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "3.1.7", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.15", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Threading.Tasks.Extensions": "4.5.1"}}, "Microsoft.AspNetCore.Mvc.Cors/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cors": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.5"}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.5", "Microsoft.Extensions.Localization": "2.2.0", "System.ComponentModel.Annotations": "4.7.0"}}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.2.0": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.5"}}, "Microsoft.AspNetCore.Mvc.Localization/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Localization": "2.2.0", "Microsoft.AspNetCore.Mvc.Razor": "2.2.0", "Microsoft.Extensions.DependencyInjection": "3.1.7", "Microsoft.Extensions.Localization": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.2.0", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Razor": "2.2.0", "Microsoft.Extensions.Caching.Memory": "3.1.7", "Microsoft.Extensions.FileProviders.Composite": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.2.0", "Microsoft.CodeAnalysis.Razor": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Mvc.RazorPages/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.2.0", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Memory": "3.1.7", "Microsoft.Extensions.FileSystemGlobbing": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Antiforgery": "2.2.0", "Microsoft.AspNetCore.Diagnostics.Abstractions": "2.2.0", "Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.5", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.Extensions.WebEncoders": "2.2.0", "Newtonsoft.Json.Bson": "1.0.1"}}, "Microsoft.AspNetCore.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Razor.Design/2.2.0": {}, "Microsoft.AspNetCore.Razor.Language/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Razor": "2.2.0"}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.AspNetCore.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.15", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "3.1.7"}}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.Bcl.HashCode/1.1.0": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {}, "Microsoft.CodeAnalysis.Common/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "1.1.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Collections.Immutable": "1.7.1", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.FileVersionInfo": "4.3.0", "System.Diagnostics.StackTrace": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Globalization": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.6.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.CodePages": "4.7.1", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0", "System.Threading.Thread": "4.3.0", "System.ValueTuple": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.0.62830"}}}, "Microsoft.CodeAnalysis.CSharp/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "2.8.0"}, "runtime": {"lib/netstandard1.3/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.0.62830"}}}, "Microsoft.CodeAnalysis.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.2.0", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Common": "2.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.18316"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/2.1.1": {"dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": "2.1.1", "Microsoft.Identity.Client": "4.21.1", "Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.8.0", "Microsoft.Win32.Registry": "5.0.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Runtime.Caching": "4.7.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "4.7.1"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.Data.Sqlite/5.0.5": {"dependencies": {"Microsoft.Data.Sqlite.Core": "5.0.5", "SQLitePCLRaw.bundle_e_sqlite3": "2.0.4"}}, "Microsoft.Data.Sqlite.Core/5.0.5": {"dependencies": {"SQLitePCLRaw.core": "2.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "5.0.5.0", "fileVersion": "5.0.521.16102"}}}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"dependencies": {"System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "Microsoft.EntityFrameworkCore/3.1.7": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "Microsoft.Bcl.HashCode": "1.1.0", "Microsoft.EntityFrameworkCore.Abstractions": "3.1.7", "Microsoft.EntityFrameworkCore.Analyzers": "3.1.7", "Microsoft.Extensions.Caching.Memory": "3.1.7", "Microsoft.Extensions.DependencyInjection": "3.1.7", "Microsoft.Extensions.Logging": "3.1.7", "System.Collections.Immutable": "1.7.1", "System.ComponentModel.Annotations": "4.7.0", "System.Diagnostics.DiagnosticSource": "4.7.1"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.720.37109"}}}, "Microsoft.EntityFrameworkCore.Abstractions/3.1.7": {"runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.720.37109"}}}, "Microsoft.EntityFrameworkCore.Analyzers/3.1.7": {}, "Microsoft.EntityFrameworkCore.Relational/3.1.7": {"dependencies": {"Microsoft.EntityFrameworkCore": "3.1.7"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.720.37109"}}}, "Microsoft.Extensions.Caching.Abstractions/3.1.7": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.720.37108"}}}, "Microsoft.Extensions.Caching.Memory/3.1.7": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "3.1.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.15", "Microsoft.Extensions.Logging.Abstractions": "3.1.15", "Microsoft.Extensions.Options": "3.1.7"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.720.37108"}}}, "Microsoft.Extensions.Configuration/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.Binder/3.1.7": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.720.37108"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.2.4": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "System.Text.Json": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.DependencyInjection/3.1.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.15"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.720.37108"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.15": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.1521.21405"}}}, "Microsoft.Extensions.DependencyModel/2.1.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "13.0.1", "System.Diagnostics.Debug": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Linq": "4.3.0"}, "runtime": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.FileProviders.Composite/2.2.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileSystemGlobbing": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Hosting.Abstractions/3.1.15": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.15", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.15"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.1521.21405"}}}, "Microsoft.Extensions.Http/3.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.15", "Microsoft.Extensions.Logging": "3.1.7", "Microsoft.Extensions.Options": "3.1.7"}}, "Microsoft.Extensions.Localization/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.15", "Microsoft.Extensions.Localization.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "3.1.15", "Microsoft.Extensions.Options": "3.1.7"}}, "Microsoft.Extensions.Localization.Abstractions/2.2.0": {}, "Microsoft.Extensions.Logging/3.1.7": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "3.1.7", "Microsoft.Extensions.DependencyInjection": "3.1.7", "Microsoft.Extensions.Logging.Abstractions": "3.1.15", "Microsoft.Extensions.Options": "3.1.7"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.720.37108"}}}, "Microsoft.Extensions.Logging.Abstractions/3.1.15": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.1521.21405"}}}, "Microsoft.Extensions.ObjectPool/2.2.0": {}, "Microsoft.Extensions.Options/3.1.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.15", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.720.37108"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Binder": "3.1.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.15", "Microsoft.Extensions.Options": "3.1.7"}}, "Microsoft.Extensions.Primitives/5.0.0": {"runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.WebEncoders/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.15", "Microsoft.Extensions.Options": "3.1.7", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.Identity.Client/4.21.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.21.1.0", "fileVersion": "4.21.1.0"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Logging/6.8.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.8.0", "System.IdentityModel.Tokens.Jwt": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Tokens/6.8.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.8.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0", "System.Buffers": "4.5.1"}}, "Microsoft.NETCore.Platforms/3.1.1": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "MongoDB.Bson/2.10.4": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Collections.NonGeneric": "4.0.1", "System.Diagnostics.Process": "4.1.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Runtime.Serialization.Formatters": "4.3.0"}, "runtime": {"lib/netstandard1.5/MongoDB.Bson.dll": {"assemblyVersion": "2.10.4.0", "fileVersion": "2.10.4.0"}}}, "MongoDB.Driver/2.10.4": {"dependencies": {"MongoDB.Bson": "2.10.4", "MongoDB.Driver.Core": "2.10.4", "MongoDB.Libmongocrypt": "1.0.0", "NETStandard.Library": "1.6.1", "System.ComponentModel.TypeConverter": "4.1.0", "System.Linq.Queryable": "4.0.1"}, "runtime": {"lib/netstandard1.5/MongoDB.Driver.dll": {"assemblyVersion": "2.10.4.0", "fileVersion": "2.10.4.0"}}}, "MongoDB.Driver.Core/2.10.4": {"dependencies": {"DnsClient": "1.3.1", "MongoDB.Bson": "2.10.4", "MongoDB.Libmongocrypt": "1.0.0", "NETStandard.Library": "1.6.1", "SharpCompress": "0.23.0", "System.Collections.Specialized": "4.0.1", "System.Diagnostics.TextWriterTraceListener": "4.0.0", "System.Diagnostics.TraceSource": "4.3.0", "System.Net.NameResolution": "4.3.0", "System.Net.Security": "4.3.2", "System.Security.SecureString": "4.0.0", "System.Threading.Thread": "4.3.0"}, "runtime": {"lib/netstandard1.5/MongoDB.Driver.Core.dll": {"assemblyVersion": "2.10.4.0", "fileVersion": "2.10.4.0"}}}, "MongoDB.Libmongocrypt/1.0.0": {"dependencies": {"NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.5/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Mono.Cecil/0.11.3": {"runtime": {"lib/netstandard2.0/Mono.Cecil.Mdb.dll": {"assemblyVersion": "0.11.3.0", "fileVersion": "0.11.3.0"}, "lib/netstandard2.0/Mono.Cecil.Pdb.dll": {"assemblyVersion": "0.11.3.0", "fileVersion": "0.11.3.0"}, "lib/netstandard2.0/Mono.Cecil.Rocks.dll": {"assemblyVersion": "0.11.3.0", "fileVersion": "0.11.3.0"}, "lib/netstandard2.0/Mono.Cecil.dll": {"assemblyVersion": "0.11.3.0", "fileVersion": "0.11.3.0"}}}, "MT.Enterprise.BPM.Reactor/1.0.10": {"dependencies": {"MT.Enterprise.Vision": "0.0.3", "Microsoft.Extensions.DependencyInjection": "3.1.7", "Newtonsoft.Json": "13.0.1", "StackExchange.Redis": "2.2.88"}, "runtime": {"lib/netstandard2.0/MT.Enterprise.BPM.Reactor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MT.Enterprise.Core/********": {"dependencies": {"AspectCore.Core": "2.2.0", "AspectCore.Extensions.DependencyInjection": "2.1.0", "MT.SqlSugar": "*******", "Microsoft.AspNetCore.Hosting": "2.2.7", "Microsoft.AspNetCore.Mvc.Core": "2.2.5", "Microsoft.Extensions.Caching.Abstractions": "3.1.7", "NLog": "4.7.4", "NLog.Extensions.Logging": "1.6.5", "NLog.Mongo": "*********", "Newtonsoft.Json": "13.0.1", "StackExchange.Redis": "2.2.88"}, "runtime": {"lib/netstandard2.1/MT.Enterprise.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MT.Enterprise.SDK/*******": {"dependencies": {"DotNetCore.CAP": "3.1.2", "DotNetCore.CAP.Dashboard": "3.1.2", "DotNetCore.CAP.MySql": "3.1.2", "DotNetCore.CAP.RabbitMQ": "3.1.2", "DotNetCore.CAP.SqlServer": "3.1.2", "HttpClientFactory": "1.0.3", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Newtonsoft.Json": "13.0.1", "StackExchange.Redis": "2.2.88"}, "runtime": {"lib/netcoreapp3.1/MT.Enterprise.SDK.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MT.Enterprise.Utils/1.0.13": {"dependencies": {"AutoMapper": "9.0.0", "DotNetCore.NPOI": "1.2.1", "Microsoft.AspNetCore.Mvc": "2.2.0", "Mono.Cecil": "0.11.3", "Newtonsoft.Json": "13.0.1", "System.IdentityModel.Tokens.Jwt": "6.8.0"}, "runtime": {"lib/netstandard2.1/MT.Enterprise.Utils.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MT.Enterprise.Vision/0.0.3": {"dependencies": {"FastExpressionCompiler": "1.10.1", "Flee": "1.2.1", "System.Linq.Dynamic.Core": "1.0.9"}, "runtime": {"lib/netstandard2.0/MT.Enterprise.Vision.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/System.ComponentModel.Composition.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.6.26419.2"}}}, "MT.SqlSugar/*******": {"dependencies": {"Microsoft.Data.SqlClient": "2.1.1", "Microsoft.Data.Sqlite": "5.0.5", "MySql.Data": "8.0.31", "Newtonsoft.Json": "13.0.1", "Npgsql": "5.0.7", "Oracle.ManagedDataAccess.Core": "3.21.1", "Oscar.Data.SqlClient": "4.0.4", "SqlSugarCore.Dm": "1.0.0", "SqlSugarCore.Kdbndp": "1.0.0", "System.Data.Common": "4.3.0", "System.Data.OleDb": "6.0.0", "System.Reflection.Emit.Lightweight": "4.3.0"}, "runtime": {"lib/netstandard2.1/SqlSugar.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MySql.Data/8.0.31": {"dependencies": {"Google.Protobuf": "3.19.4", "K4os.Compression.LZ4.Streams": "1.2.6", "Portable.BouncyCastle": "1.9.0", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Security.Permissions": "6.0.0", "System.Text.Encoding.CodePages": "4.7.1"}, "runtime": {"lib/netstandard2.1/MySql.Data.dll": {"assemblyVersion": "8.0.31.0", "fileVersion": "8.0.31.0"}, "lib/netstandard2.1/ZstdNet.dll": {"assemblyVersion": "1.4.5.0", "fileVersion": "1.4.5.0"}}}, "MySqlConnector/1.0.1": {"runtime": {"lib/netcoreapp3.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "nacos-sdk-csharp-unofficial/0.8.5": {"dependencies": {"Microsoft.Extensions.Http": "3.1.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "3.1.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Nacos.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "nacos-sdk-csharp-unofficial.AspNetCore/0.8.5": {"dependencies": {"EasyCaching.InMemory": "0.8.9", "Microsoft.Extensions.Hosting.Abstractions": "3.1.15", "nacos-sdk-csharp-unofficial": "0.8.5"}, "runtime": {"lib/netcoreapp3.1/Nacos.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "Newtonsoft.Json.Bson/1.0.1": {"dependencies": {"NETStandard.Library": "1.6.1", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard1.3/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.1.20722"}}}, "NLog/4.7.4": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.7.4.12037"}}}, "NLog.Extensions.Logging/1.6.5": {"dependencies": {"Microsoft.Extensions.Logging": "3.1.7", "NLog": "4.7.4"}, "runtime": {"lib/netcoreapp3.0/NLog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "1.6.5.1409"}}}, "NLog.Mongo/*********": {"dependencies": {"MongoDB.Driver": "2.10.4", "NLog": "4.7.4"}, "runtime": {"lib/netstandard2.0/NLog.Mongo.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Npgsql/5.0.7": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/Npgsql.dll": {"assemblyVersion": "5.0.7.0", "fileVersion": "5.0.7.0"}}}, "Oracle.ManagedDataAccess.Core/3.21.1": {"dependencies": {"System.Diagnostics.PerformanceCounter": "6.0.0", "System.DirectoryServices": "4.7.0", "System.DirectoryServices.Protocols": "4.7.0", "System.Text.Json": "5.0.0"}, "runtime": {"lib/netstandard2.1/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Oscar.Data.SqlClient/4.0.4": {"dependencies": {"System.Text.Encoding.CodePages": "4.7.1"}, "runtime": {"lib/netstandard2.0/Oscar.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pipelines.Sockets.Unofficial/2.2.0": {"dependencies": {"System.IO.Pipelines": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.45337"}}}, "Portable.BouncyCastle/1.9.0": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "RabbitMQ.Client/6.2.1": {"dependencies": {"System.Memory": "4.5.4", "System.Threading.Channels": "4.7.1"}, "runtime": {"lib/netstandard2.0/RabbitMQ.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Security/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "SharpCompress/0.23.0": {"dependencies": {"System.Text.Encoding.CodePages": "4.7.1"}, "runtime": {"lib/netstandard2.0/SharpCompress.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SharpZipLib/1.0.0": {"runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "1.0.0.999", "fileVersion": "1.0.0.999"}}}, "SmartFormat.NET/2.4.2": {"dependencies": {"Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/SmartFormat.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.0.4": {"dependencies": {"SQLitePCLRaw.core": "2.0.4", "SQLitePCLRaw.lib.e_sqlite3": "2.0.4", "SQLitePCLRaw.provider.dynamic_cdecl": "2.0.4"}, "runtime": {"lib/netcoreapp3.1/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}, "lib/netcoreapp3.1/SQLitePCLRaw.nativelibrary.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}}, "SQLitePCLRaw.core/2.0.4": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.0.4": {"runtimeTargets": {"runtimes/alpine-x64/native/libe_sqlite3.so": {"rid": "alpine-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.dynamic_cdecl/2.0.4": {"dependencies": {"SQLitePCLRaw.core": "2.0.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.provider.dynamic_cdecl.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}}, "SqlSugarCore.Dm/1.0.0": {"runtime": {"lib/netstandard2.0/DmProvider.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.0.42711"}}}, "SqlSugarCore.Kdbndp/1.0.0": {"runtime": {"lib/netstandard2.0/Kdbndp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "StackExchange.Redis/2.2.88": {"dependencies": {"Pipelines.Sockets.Unofficial": "2.2.0", "System.Diagnostics.PerformanceCounter": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.88.56325"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.1": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/1.7.1": {"runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"assemblyVersion": "1.2.5.0", "fileVersion": "4.700.20.21406"}}}, "System.Collections.NonGeneric/4.0.1": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.0.1": {"dependencies": {"System.Collections.NonGeneric": "4.0.1", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.ComponentModel.Annotations/4.7.0": {}, "System.ComponentModel.Primitives/4.1.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ComponentModel.TypeConverter/4.1.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.0.1", "System.Collections.Specialized": "4.0.1", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.1.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Configuration.ConfigurationManager/6.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Data.OleDb/6.0.0": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Diagnostics.PerformanceCounter": "6.0.0", "System.Memory": "4.5.4", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Data.OleDb.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Data.OleDb.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/4.7.1": {"runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "System.Diagnostics.FileVersionInfo/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Reflection.Metadata": "1.6.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.Diagnostics.PerformanceCounter/6.0.0": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.1/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Diagnostics.Process/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.Win32.Primitives": "4.3.0", "Microsoft.Win32.Registry": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.StackTrace/4.3.0": {"dependencies": {"System.IO.FileSystem": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.6.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.TextWriterTraceListener/4.0.0": {"dependencies": {"System.Diagnostics.TraceSource": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.TraceSource/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.DirectoryServices/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "System.IO.FileSystem.AccessControl": "4.7.0", "System.Security.AccessControl": "6.0.0", "System.Security.Permissions": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.DirectoryServices.Protocols/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/netcoreapp3.1/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "System.Buffers": "4.5.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.1", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.AccessControl/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Pipelines/5.0.0": {"runtime": {"lib/netcoreapp3.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.0.9": {"dependencies": {"System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.0.1": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.NameResolution/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "5.0.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Security/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Security.Principal": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Security": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.ILGeneration/4.7.0": {}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/1.6.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/4.7.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Formatters/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {"dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/4.5.0": {"dependencies": {"System.Security.Cryptography.Cng": "4.5.0"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Xml/4.5.0": {"dependencies": {"System.Security.Cryptography.Pkcs": "4.5.0", "System.Security.Permissions": "6.0.0"}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Security.Principal.Windows/5.0.0": {"runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.SecureString/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/4.7.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1"}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/4.5.0": {}, "System.Text.Json/5.0.0": {"runtime": {"lib/netcoreapp3.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels/4.7.1": {"runtime": {"lib/netcoreapp3.0/System.Threading.Channels.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.700.20.21406"}}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.1": {}, "System.Threading.Tasks.Parallel/4.3.0": {"dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.ValueTuple/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.1/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.1"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XPath/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XPath.XDocument/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath": "4.3.0"}}, "Medusa.Service.Modeling.Application/1.0.0": {"dependencies": {"Medusa.Service.Modeling.Core": "1.0.0", "Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.Extensions.Hosting.Abstractions": "3.1.15", "SmartFormat.NET": "2.4.2"}, "runtime": {"Medusa.Service.Modeling.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Medusa.Service.Modeling.Core/1.0.0": {"dependencies": {"MT.Enterprise.BPM.Reactor": "1.0.10", "MT.Enterprise.Core": "********", "MT.Enterprise.SDK": "*******", "MT.Enterprise.Utils": "1.0.13", "Medusa.Service.Cache": "********", "nacos-sdk-csharp-unofficial.AspNetCore": "0.8.5"}, "runtime": {"Medusa.Service.Modeling.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Medusa.Service.Modeling.Connect/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AspectCore.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7oeO2eKAbfd3M3K3vcZqZAi6wNRGjHgSO1J6XTLnY/nyP08KgSvhHbl0wLSuaOeigQau27wzy/DLbAGo8MSKRQ==", "path": "aspectcore.abstractions/2.2.0", "hashPath": "aspectcore.abstractions.2.2.0.nupkg.sha512"}, "AspectCore.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-qEjaVRVv6AUVEffHov9KcVwo66LjQg6NyZKKgJzj3qEN2y7lBykvSRQUac97PPXoJU4pVxl2vNdr4ERSe6Rp/A==", "path": "aspectcore.core/2.2.0", "hashPath": "aspectcore.core.2.2.0.nupkg.sha512"}, "AspectCore.Extensions.DependencyInjection/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-sQyP5obCI9F3QtNWd/QhVROw58EifQSjPdO/jiaDD2sTOnjyAVX01l2h6wXN5OWQ6jA8ECiT6ZQMep/RHv6ZQg==", "path": "aspectcore.extensions.dependencyinjection/2.1.0", "hashPath": "aspectcore.extensions.dependencyinjection.2.1.0.nupkg.sha512"}, "AspectCore.Extensions.Reflection/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-S+cDbXIDy0JzMFjnPSOMrFDeRpa+Xz8xIxDG3oeCztaerMShl5WFBngQfC+8XEnapsyvtNsWpvSw1rj9WmVHOg==", "path": "aspectcore.extensions.reflection/2.2.0", "hashPath": "aspectcore.extensions.reflection.2.2.0.nupkg.sha512"}, "AutoMapper/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xCqvoxT4HIrNY/xlXG9W+BA/awdrhWvMTKTK/igkGSRbhOhpl3Q8O8Gxlhzjc9JsYqE7sS6AxgyuUUvZ6R5/Bw==", "path": "automapper/9.0.0", "hashPath": "automapper.9.0.0.nupkg.sha512"}, "Consul/*******": {"type": "package", "serviceable": true, "sha512": "sha512-AJkBiix1bT3cSZRO6OD0Ee31Co1qgVkmVBFwpxym9WdxH5K6LfmAZ8Vb8nGvo9EKC+ir2sv5fKIrY75aENcMDA==", "path": "consul/*******", "hashPath": "consul.*******.nupkg.sha512"}, "DnsClient/1.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-b9XczgqSjs3dd+OlV799jn+LXxdfirln1q36GKjs3QwWBhjhgIvgHLEG5s2VrBLcP4ogQjtA0PZ5+AmRJ35Vhg==", "path": "dnsclient/1.3.1", "hashPath": "dnsclient.1.3.1.nupkg.sha512"}, "DotNetCore.CAP/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-LFgUxYNuoQmROiZgzz5oWR1n1uxuaM4/2j8B7DJ1t+pYpsJRIba3SXBbcIf89cHQ0I7zZJwTq2hnMFtp36dN0w==", "path": "dotnetcore.cap/3.1.2", "hashPath": "dotnetcore.cap.3.1.2.nupkg.sha512"}, "DotNetCore.CAP.Dashboard/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-eonh3kjFOaol1qSyTrPhPSLFNoZXiSEN3kDf/phLVNaE9/RpRndVXnj8LRYm0QvjBFXZlr05PHEBbzw3EWgf8w==", "path": "dotnetcore.cap.dashboard/3.1.2", "hashPath": "dotnetcore.cap.dashboard.3.1.2.nupkg.sha512"}, "DotNetCore.CAP.MySql/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6iGJwiOtdZM5/muh67yd9LASn3SZqHJHffbm9bIQK0QCSGX+85EUbzveOTm3mVuPgQDY0TdrGbUpecLrYmU3ww==", "path": "dotnetcore.cap.mysql/3.1.2", "hashPath": "dotnetcore.cap.mysql.3.1.2.nupkg.sha512"}, "DotNetCore.CAP.RabbitMQ/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-C3ptlzAvAVoAe4EMtq6DLQ4zSb8IvRn/MiDDU7BMfADvUGdao50fgkGmSHLD/0JSDZOwQid4shC6gkZGPPZGHQ==", "path": "dotnetcore.cap.rabbitmq/3.1.2", "hashPath": "dotnetcore.cap.rabbitmq.3.1.2.nupkg.sha512"}, "DotNetCore.CAP.SqlServer/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-HJBun0pDmVwZX6KREPEqzVfmVU88uKpA4arXFzA5MRXsp0KYeCzx9SCOM/1d6Esr/dmgUjcABEiCi8bTbP9NgQ==", "path": "dotnetcore.cap.sqlserver/3.1.2", "hashPath": "dotnetcore.cap.sqlserver.3.1.2.nupkg.sha512"}, "DotNetCore.NPOI/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-2AMq9Dd3rD4pZlU3sT4EveRLzXFyw90OO5ShUh+IqBisrSssN4l0tYIrD6XEKL/J5gdlIxB/JsjKk7ieUCIW6g==", "path": "dotnetcore.npoi/1.2.1", "hashPath": "dotnetcore.npoi.1.2.1.nupkg.sha512"}, "DotNetCore.NPOI.Core/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-/6t1vRqmxmvjlNolwyhfYbhQARZKiwmzx3bBoyhr3dZ/k3cod3hFxCsOwFczq4yGmFONiNFDOZBzcM+TNXL55w==", "path": "dotnetcore.npoi.core/1.2.1", "hashPath": "dotnetcore.npoi.core.1.2.1.nupkg.sha512"}, "DotNetCore.NPOI.OpenXml4Net/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KbqwRjatdEKmhzMFeRby5iHxh4DRuLu3VYqzah3RTCNfleczQjlsZwMM9DWQO1lUtdU1Q2keJm1IYAK92zfLqg==", "path": "dotnetcore.npoi.openxml4net/1.2.1", "hashPath": "dotnetcore.npoi.openxml4net.1.2.1.nupkg.sha512"}, "DotNetCore.NPOI.OpenXmlFormats/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-uHynYPi/iDeQbHapCOrmTGBJRX4Il8F0qeCx3Xbqo/bo0FazY2dKGhD6CWb07ZCGe1DDgu4bmMRv/1l4/21+wQ==", "path": "dotnetcore.npoi.openxmlformats/1.2.1", "hashPath": "dotnetcore.npoi.openxmlformats.1.2.1.nupkg.sha512"}, "EasyCaching.Core/0.8.9": {"type": "package", "serviceable": true, "sha512": "sha512-2q0Kvp2eDtG9rSToB0zIltOZc3dpBytiF3kVwItEOv//aDzTpHPVhSFVqP62wG0MuRfmdWBRoptRPdNmeVggyw==", "path": "easycaching.core/0.8.9", "hashPath": "easycaching.core.0.8.9.nupkg.sha512"}, "EasyCaching.InMemory/0.8.9": {"type": "package", "serviceable": true, "sha512": "sha512-wM+oRU1LLPiDCgyJa6OkJFixi0NrPolT8Rs4UY/Z80ZooLJrQ0hHO+Wk4J5We5Gjr0cdGlkHwRoKgfjeHyFbqQ==", "path": "easycaching.inmemory/0.8.9", "hashPath": "easycaching.inmemory.0.8.9.nupkg.sha512"}, "FastExpressionCompiler/1.10.1": {"type": "package", "serviceable": true, "sha512": "sha512-eKx6sgmLKAdA4bKWXY8azLTxQz9MStDsbPBHY3wJ8ABNxpLG7w8eWBuITumJK3mI8c/9cdFod06RliDYuGLzzg==", "path": "fastexpressioncompiler/1.10.1", "hashPath": "fastexpressioncompiler.1.10.1.nupkg.sha512"}, "Flee/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-xMFYAwkSvtBmUazr4RkEGJSyKLl4K4r1fcjkD9Rhe5QNKjZB7DkGHtqLfJDwRiQoGN/cm/6zRDDNVgaCp7WEZA==", "path": "flee/1.2.1", "hashPath": "flee.1.2.1.nupkg.sha512"}, "Google.Protobuf/3.19.4": {"type": "package", "serviceable": true, "sha512": "sha512-fd07/ykL4O4FhqrZIELm5lmiyOHfdPg9+o+hWr6tcfRdS7tHXnImg/2wtogLzlW2eEmr0J7j6ZrZvaWOLiJbxQ==", "path": "google.protobuf/3.19.4", "hashPath": "google.protobuf.3.19.4.nupkg.sha512"}, "HttpClientFactory/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-12pNr6uQoKv1FJnguWJ1WuKFCS+z5DrYW1twF25Ix2w2w8houGYUja4VPNkPRUXojEsLD46t68BnuDLirtBB9Q==", "path": "httpclientfactory/1.0.3", "hashPath": "httpclientfactory.1.0.3.nupkg.sha512"}, "K4os.Compression.LZ4/1.2.6": {"type": "package", "serviceable": true, "sha512": "sha512-4EN8EE6bZG2U8dFfeqn+Om3UNajK3cPYHvyQROCFm4jNFVLuRB7Nl5bDkjBSAjfctS6konm+ay3u5RafBzltDA==", "path": "k4os.compression.lz4/1.2.6", "hashPath": "k4os.compression.lz4.1.2.6.nupkg.sha512"}, "K4os.Compression.LZ4.Streams/1.2.6": {"type": "package", "serviceable": true, "sha512": "sha512-5KMcNFRHeRrnJ9c8k5fZcfAJJEY0FndMiDiHIYa35Mx5KCMkeSNo/PEXu7YmtCoVczJagx+Vt7J/F+//S1PcJQ==", "path": "k4os.compression.lz4.streams/1.2.6", "hashPath": "k4os.compression.lz4.streams.1.2.6.nupkg.sha512"}, "K4os.Hash.xxHash/1.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-jCfNP0inx1sGcP3KSbpiDEH3km2e1sVBjMfKo+V92jr1dL4ZYgA1uhRMl1wAtdGZcbObXIikKqtVlgx3j/CW6g==", "path": "k4os.hash.xxhash/1.0.6", "hashPath": "k4os.hash.xxhash.1.0.6.nupkg.sha512"}, "Medusa.Service.Cache/********": {"type": "package", "serviceable": true, "sha512": "sha512-3SvvWHB3tdkUbHWCujkpBAhryb2IF0oJUXIit8YDNXDsEFVinQykkVqXplCqvsSZIlQSQxIXn3GgRog4bxGaYQ==", "path": "medusa.service.cache/********", "hashPath": "medusa.service.cache.********.nupkg.sha512"}, "Microsoft.AspNetCore.Antiforgery/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-fVQsSXNZz38Ysx8iKwwqfOLHhLrAeKEMBS5Ia3Lh7BJjOC2vPV28/yk08AovOMsB3SNQPGnE7bv+lsIBTmAkvw==", "path": "microsoft.aspnetcore.antiforgery/2.2.0", "hashPath": "microsoft.aspnetcore.antiforgery.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-/L0W8H3jMYWyaeA9gBJqS/tSWBegP9aaTM0mjRhxTttBY9z4RVDRYJ2CwPAmAXIuPr3r1sOw+CS8jFVRGHRezQ==", "path": "microsoft.aspnetcore.authorization/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "path": "microsoft.aspnetcore.authorization.policy/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cors/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFlTM3ThS3ZCILuKnjy8HyK9/IlDh3opogdbCVx6tMGyDzTQBgMPXLjGDLtMk5QmLDCcP3l1TO3z/+1viA8GUg==", "path": "microsoft.aspnetcore.cors/2.2.0", "hashPath": "microsoft.aspnetcore.cors.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GXmMD8/vuTLPLvKzKEPz/4vapC5e0cwx1tUVd83ePRyWF9CCrn/pg4/1I+tGkQqFLPvi3nlI2QtPtC6MQN8Nww==", "path": "microsoft.aspnetcore.cryptography.internal/2.2.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-G6dvu5Nd2vjpYbzazZ//qBFbSEf2wmBUbyAR7E4AwO3gWjhoJD5YxpThcGJb7oE3VUcW65SVMXT+cPCiiBg8Sg==", "path": "microsoft.aspnetcore.dataprotection/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-seANFXmp8mb5Y12m1ShiElJ3ZdOT3mBN3wA1GPhHJIvZ/BxOCPyqEOR+810OWsxEZwA5r5fDRNpG/CqiJmQnJg==", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-pva9ggfUDtnJIKzv0+wxwTX7LduDx6xLSpMqWwdOJkW52L0t31PI78+v+WqqMpUtMzcKug24jGs3nTFpAmA/2g==", "path": "microsoft.aspnetcore.diagnostics.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.diagnostics.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting/2.2.7": {"type": "package", "serviceable": true, "sha512": "sha512-O0ZBE53Fa9bVGXykDzvgMFW0Pe1QyPPFg1pazN8l3RUFSWBsDJ9/iD1LHXgADA8+ZD3R/1zkvraPa9SZdievxQ==", "path": "microsoft.aspnetcore.hosting/2.2.7", "hashPath": "microsoft.aspnetcore.hosting.2.2.7.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y4rs5aMEXY8G7wJo5S3EEt6ltqyOTr/qOeZzfn+hw/fuQj5GppGckMY5psGLETo1U9hcT5MmAhaT5xtusM1b5g==", "path": "microsoft.aspnetcore.html.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.html.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YogBSMotWPAS/X5967pZ+yyWPQkThxhmzAwyCHCSSldzYBkW5W5d6oPfBaPqQOnSHYTpSOSOkpZoAce0vwb6+A==", "path": "microsoft.aspnetcore.http/2.2.0", "hashPath": "microsoft.aspnetcore.http.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-o9BB9hftnCsyJalz9IT0DUFxz8Xvgh3TOfGWolpuf19duxB4FySq7c25XDYBmBMS+sun5/PsEUAi58ra4iJAoA==", "path": "microsoft.aspnetcore.jsonpatch/2.2.0", "hashPath": "microsoft.aspnetcore.jsonpatch.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Localization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+PGX1mEfq19EVvskBBb9XBQrXZpZrh6hYhX0x3FkPTEqr+rDM2ZmsEwAAMRmzcidmlDM1/7cyDSU/WhkecU8tA==", "path": "microsoft.aspnetcore.localization/2.2.0", "hashPath": "microsoft.aspnetcore.localization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-noun9xcrEvOs/ubczt2OluY9/bOOM2erv1D/gyyYtfS2sfyx2uGknUIAWoqmqc401TvQDysyx8S4M9j5zPIVBw==", "path": "microsoft.aspnetcore.mvc/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "path": "microsoft.aspnetcore.mvc.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Analyzers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wxxt1rFVHITp4MDaGQP/wyl+ROVVVeQCTWI6C8hxI8X66C4u6gcxvelqgnmsn+dISMCdE/7FQOwgiMx1HxuZqA==", "path": "microsoft.aspnetcore.mvc.analyzers/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.analyzers.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iSREQct43Xg2t3KiQ2648e064al/HSLPXpI5yO9VPeTGDspWKHW23XFHRKPN1YjIQHHfBj8ytXbiF0XcSxp5pg==", "path": "microsoft.aspnetcore.mvc.apiexplorer/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.apiexplorer.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-/8sr8ixIUD57UFwUntha9bOwex7/AkZfdk1f9oNJG1Ek7p/uuKVa7fuHmYZpQOf35Oxrt+2Ku4WPwMSbNxOuWg==", "path": "microsoft.aspnetcore.mvc.core/2.2.5", "hashPath": "microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Cors/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-oINjMqhU7yzT2T9AMuvktlWlMd40i0do8E1aYslJS+c5fof+EMhjnwTh6cHN1dfrgjkoXJ/gutxn5Qaqf/81Kg==", "path": "microsoft.aspnetcore.mvc.cors/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.cors.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-WOw4SA3oT47aiU7ZjN/88j+b79YU6VftmHmxK29Km3PTI7WZdmw675QTcgWfsjEX4joCB82v7TvarO3D0oqOyw==", "path": "microsoft.aspnetcore.mvc.dataannotations/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.dataannotations.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ScWwXrkAvw6PekWUFkIr5qa9NKn4uZGRvxtt3DvtUrBYW5Iu2y4SS/vx79JN0XDHNYgAJ81nVs+4M7UE1Y/O+g==", "path": "microsoft.aspnetcore.mvc.formatters.json/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.formatters.json.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Localization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-H1L4pP124mrN6duwOtNVIJUqy4CczC2/ah4MXarRt9ZRpJd2zNp1j3tJCgyEQpqai6zNVP6Vp2ZRMQcNDcNAKA==", "path": "microsoft.aspnetcore.mvc.localization/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.localization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-TXvEOjp3r6qDEjmDtv3pXjQr/Zia9PpoGkl1MyTEqKqrUehBTpAdCjA8APXFwun19lH20OuyU+e4zDYv9g134w==", "path": "microsoft.aspnetcore.mvc.razor/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.razor.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Sei/0moqBDQKaAYT9PtOeRtvYgHQQLyw/jm3exHw2w9VdzejiMEqCQrN2d63Dk4y7IY0Irr/P9JUFkoVURRcNw==", "path": "microsoft.aspnetcore.mvc.razor.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.razor.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.RazorPages/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GsMs4QKCf5VgdGZq9/nfAVkMJ/8uE4ie0Iugv4FtxbHBmMdpPQQBfTFKoUpwMbgIRw7hzV8xy2HPPU5o58PsdQ==", "path": "microsoft.aspnetcore.mvc.razorpages/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.razorpages.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hsrm/dLx7ztfWV+WEE7O8YqEePW7TmUwFwR7JsOUSTKaV9uSeghdmoOsYuk0HeoTiMhRxH8InQVE9/BgBj+jog==", "path": "microsoft.aspnetcore.mvc.taghelpers/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.taghelpers.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-dt7MGkzCFVTAD5oesI8UeVVeiSgaZ0tPdFstQjG6YLJSCiq1koOUSHMpf0PASGdOW/H9hxXkolIBhT5dWqJi7g==", "path": "microsoft.aspnetcore.mvc.viewfeatures/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.viewfeatures.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V54PIyDCFl8COnTp9gezNHpUNHk7F9UnerGeZy3UfbnwYvfzbo+ipqQmSgeoESH8e0JvKhRTyQyZquW2EPtCmg==", "path": "microsoft.aspnetcore.razor/2.2.0", "hashPath": "microsoft.aspnetcore.razor.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Design/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VLWK+ZtMMNukY6XjxYHc7mz33vkquoEzQJHm/LCF5REVxIaexLr+UTImljRRJBdUDJluDAQwU+59IX0rFDfURA==", "path": "microsoft.aspnetcore.razor.design/2.2.0", "hashPath": "microsoft.aspnetcore.razor.design.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeyzVFXZdpUAnWKWoNYE0SsP1Eu7JLjZaC94jaI1VfGtK57QykROz/iGMc8D0VcqC8i02qYTPQN/wPKm6PfidA==", "path": "microsoft.aspnetcore.razor.language/2.2.0", "hashPath": "microsoft.aspnetcore.razor.language.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7YqK+H61lN6yj9RiQUko7oaOhKtRR9Q/kBcoWNRemhJdTIWOh1OmdvJKzZrMWOlff3BAjejkPQm+0V0qXk+B1w==", "path": "microsoft.aspnetcore.razor.runtime/2.2.0", "hashPath": "microsoft.aspnetcore.razor.runtime.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "path": "microsoft.aspnetcore.routing/2.2.0", "hashPath": "microsoft.aspnetcore.routing.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-J2G1k+u5unBV+aYcwxo94ip16Rkp65pgWFb0R6zwJipzWNMgvqlWeuI7/+R+e8bob66LnSG+llLJ+z8wI94cHg==", "path": "microsoft.bcl.hashcode/1.1.0", "hashPath": "microsoft.bcl.hashcode.1.1.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-HS3iRWZKcUw/8eZ/08GXKY2Bn7xNzQPzf8gRPHGSowX7u7XXu9i9YEaBeBNKUXWfI7qjvT2zXtLUvbN0hds8vg==", "path": "microsoft.codeanalysis.analyzers/1.1.0", "hashPath": "microsoft.codeanalysis.analyzers.1.1.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-06AzG7oOLKTCN1EnoVYL1bQz+Zwa10LMpUn7Kc+PdpN8CQXRqXTyhfxuKIz6t0qWfoatBNXdHD0OLcEYp5pOvQ==", "path": "microsoft.codeanalysis.common/2.8.0", "hashPath": "microsoft.codeanalysis.common.2.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-RizcFXuHgGmeuZhxxE1qQdhFA9lGOHlk0MJlCUt6LOnYsevo72gNikPcbANFHY02YK8L/buNrihchY0TroGvXQ==", "path": "microsoft.codeanalysis.csharp/2.8.0", "hashPath": "microsoft.codeanalysis.csharp.2.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2qL0Qyu5qHzg6/JzF80mLgsqn9NP/Q0mQwjH+Z+DiqcuODJx8segjN4un2Tnz6bEAWv8FCRFNXR/s5wzlxqA8A==", "path": "microsoft.codeanalysis.razor/2.2.0", "hashPath": "microsoft.codeanalysis.razor.2.2.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-qxPmA2q0/oqqzZiwgN2QcoFRMPPQOCOxJP9h8X/bLXkPRsIo8xy182td9Txt0WhobW1dBBSYj96/Wf9cmhpm7Q==", "path": "microsoft.data.sqlclient/2.1.1", "hashPath": "microsoft.data.sqlclient.2.1.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-JwGDWkyZgm7SATJmFLfT2G4teimvNbNtq3lsS9a5DzvhEZnQrZjZhevCU0vdx8MjheLHoG5vocuO03QtioFQxQ==", "path": "microsoft.data.sqlclient.sni.runtime/2.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.2.1.1.nupkg.sha512"}, "Microsoft.Data.Sqlite/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-zTeCkFsBHZ1/iBd0GqyAUrtb3xuaiUeJyhd9hjuW9yo/ylRhWqxORKznR0bR1g/joUTohGTHAXr/KIuSNyjH/Q==", "path": "microsoft.data.sqlite/5.0.5", "hashPath": "microsoft.data.sqlite.5.0.5.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-tFKcgzzk3495LzD38gw75qmFS6Y1lDr5O9TGfSSG8GgtYF2G5VuTp7VdkeHKaaKOOgrSgHjuc3ogyWh7TZ10Hg==", "path": "microsoft.data.sqlite.core/5.0.5", "hashPath": "microsoft.data.sqlite.core.5.0.5.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-9KPDwvb/hLEVXYruVHVZ8BkebC8j17DmPb56LnqRF74HqSPLjCkrlFUjOtFpQPA2DeADBRTI/e69aCfRBfrhxw==", "path": "microsoft.dotnet.platformabstractions/2.1.0", "hashPath": "microsoft.dotnet.platformabstractions.2.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-u0MXvEkUwXd/JPzuWcSn4Bvx65FrDsn0GJV/tN4kjoZdVcKkXgbDG+RZM9BMGgK77NXmhx9Osy35fujmS3otyA==", "path": "microsoft.entityframeworkcore/3.1.7", "hashPath": "microsoft.entityframeworkcore.3.1.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-Dx2aGjTKPq49nMYl8t9Lq9XgZJOSIsIQE7lOCThVyHHAYgZrkc8NRgzE/PLGfUO4tfyNSImkXlyF98DBAXI5fw==", "path": "microsoft.entityframeworkcore.abstractions/3.1.7", "hashPath": "microsoft.entityframeworkcore.abstractions.3.1.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-zSh3QF/Kz8fdVgyStQ1HYUGFDRqFbUR1UBpfa8tUuB0OutYab9R4aYdjkD8i9w9P5VZyHXqQNglh2cQHBFLtjg==", "path": "microsoft.entityframeworkcore.analyzers/3.1.7", "hashPath": "microsoft.entityframeworkcore.analyzers.3.1.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-XLzwdgHPq5QgcmPZkkAso+SSW/tQqPD/iGaZv+fhl7lCb7VTPsjGE0BiPG9xiUP7OR+tlKNDffSzKimElZMaqA==", "path": "microsoft.entityframeworkcore.relational/3.1.7", "hashPath": "microsoft.entityframeworkcore.relational.3.1.7.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-Uj/0fmq7FWi7B7RfLkn2UJE1SThJ60mOlChx9P5nRjP/EbeYNm+LoyiOr6yPhDsArwHttKyun18TXXJ2C9EE/g==", "path": "microsoft.extensions.caching.abstractions/3.1.7", "hashPath": "microsoft.extensions.caching.abstractions.3.1.7.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-EvNcJWk0KKSqyXefiUTgF5D94r40rmmtAvo5/yhy7u5/CtRjqyN7wQBSKz2ezWlu5vbuMGyaMktANgkk4kEkaw==", "path": "microsoft.extensions.caching.memory/3.1.7", "hashPath": "microsoft.extensions.caching.memory.3.1.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LN322qEKHjuVEhhXueTUe7RNePooZmS8aGid5aK2woX3NPjSnONFyKUc6+JknOS6ce6h2tCLfKPTBXE3mN/6Ag==", "path": "microsoft.extensions.configuration/5.0.0", "hashPath": "microsoft.extensions.configuration.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-FYQ64i7DwYO6XDdOFi+VkoRDDzel570I+X/vTYt6mVJWNj1SAstXwZ71P5d6P2Cei+cT950NiScOwj4F8EUeTA==", "path": "microsoft.extensions.configuration.binder/3.1.7", "hashPath": "microsoft.extensions.configuration.binder.3.1.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-Os7uRhp9xwawY5w2tgXw/86YrmAJZl6aHiQVdS9boWybTWPkJOvXXrQ3AGwuldN1W/r+cfnwRe2ePGeFO4zlzg==", "path": "microsoft.extensions.configuration.environmentvariables/2.2.4", "hashPath": "microsoft.extensions.configuration.environmentvariables.2.2.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rRdspYKA18ViPOISwAihhCMbusHsARCOtDMwa23f+BGEdIjpKPlhs3LLjmKlxfhpGXBjIsS0JpXcChjRUN+PAw==", "path": "microsoft.extensions.configuration.fileextensions/5.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pak8ymSUfdzPfBTLHxeOwcR32YDbuVfhnH2hkfOLnJNQd19ItlBdpMjIDY9C5O/nS2Sn9bzDMai0ZrvF7KyY/Q==", "path": "microsoft.extensions.configuration.json/5.0.0", "hashPath": "microsoft.extensions.configuration.json.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-yp38AFc5tJQZkjINjXBcxq+dANU06lo27D84duitZthtRPsgKJL87uf9RWRsDdRsWd+kXflmdMWFLKFjyKx6Pw==", "path": "microsoft.extensions.dependencyinjection/3.1.7", "hashPath": "microsoft.extensions.dependencyinjection.3.1.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.15": {"type": "package", "serviceable": true, "sha512": "sha512-LqpcQzbu/L8GYrq0eDb8sFe0tjLMngsDycd6iheY833Wy6eGP3b45HmgDULq9ZT+SYlz2lyk4ZDt/7U3ZwGK3Q==", "path": "microsoft.extensions.dependencyinjection.abstractions/3.1.15", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.3.1.15.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-nS2XKqi+1A1umnYNLX2Fbm/XnzCxs5i+zXVJ3VC6r9t2z0NZr9FLnJN4VQpKigdcWH/iFTbMuX6M6WQJcTjVIg==", "path": "microsoft.extensions.dependencymodel/2.1.0", "hashPath": "microsoft.extensions.dependencymodel.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iuZIiZ3mteEb+nsUqpGXKx2cGF+cv6gWPd5jqQI4hzqdiJ6I94ddLjKhQOuRW1lueHwocIw30xbSHGhQj0zjdQ==", "path": "microsoft.extensions.fileproviders.abstractions/5.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Az/RxWB+UlyVN/TvQFaGXx8XAXVZN5WQnnuJOsjwBzghSJc1i8zqNjIypPHOedcuIXs2XSWgOSL6YQ3BlCnoJA==", "path": "microsoft.extensions.fileproviders.composite/2.2.0", "hashPath": "microsoft.extensions.fileproviders.composite.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1rkd8UO2qf21biwO7X0hL9uHP7vtfmdv/NLvKgCRHkdz1XnW8zVQJXyEYiN68WYpExgtVWn55QF0qBzgfh1mGg==", "path": "microsoft.extensions.fileproviders.physical/5.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ArliS8lGk8sWRtrWpqI8yUVYJpRruPjCDT+EIjrgkA/AAPRctlAkRISVZ334chAKktTLzD1+PK8F5IZpGedSqA==", "path": "microsoft.extensions.filesystemglobbing/5.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/3.1.15": {"type": "package", "serviceable": true, "sha512": "sha512-v/w0S4jH055XpJ7vwKKwbPyYLVe2d3VWR6v5VpFARQcpWY/yfNWeng9uNOrZZ2DSE4AG9HHwivCa3uiCKv2vDA==", "path": "microsoft.extensions.hosting.abstractions/3.1.15", "hashPath": "microsoft.extensions.hosting.abstractions.3.1.15.nupkg.sha512"}, "Microsoft.Extensions.Http/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-DLigdcV0nYaT6/ly0rnfP80BnXq8NNd/h8/SkfY39uio7Bd9LauVntp6RcRh1Kj23N+uf80GgL7Win6P3BCtoQ==", "path": "microsoft.extensions.http/3.1.0", "hashPath": "microsoft.extensions.http.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-3nBQLeBrcd4Rgd9vQi4gF5NgAWxnQrHekjjwlgww4wyLNfJDizjiex2resOLoAuAgy3y2IIAWjOpbr0UKR2ykw==", "path": "microsoft.extensions.localization/2.2.0", "hashPath": "microsoft.extensions.localization.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-FQzXG/lYR9UOM2zHpqsjTRpp3EghIYo3FCsQpfmtbp+glPaU0WXZfNmMjyqBRmMj1Sq93fPnC+G9zzYRauuRQA==", "path": "microsoft.extensions.localization.abstractions/2.2.0", "hashPath": "microsoft.extensions.localization.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-p68Hvr8S+t+bGUeXRX6wcjlkK961w0YRXWy8O6pkTsucdNpnzB8mwLIgcnQ7oj0biw8S1Ftv4PREzPIwo1UwpA==", "path": "microsoft.extensions.logging/3.1.7", "hashPath": "microsoft.extensions.logging.3.1.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/3.1.15": {"type": "package", "serviceable": true, "sha512": "sha512-xYHeWtqrTRpSdYbJToxacxL1rZEVXKyurlOhDJobnSlfMVt/uww5TPhxkHahenGvOE2UPaIfyvi2HFnBoPv98g==", "path": "microsoft.extensions.logging.abstractions/3.1.15", "hashPath": "microsoft.extensions.logging.abstractions.3.1.15.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-O5TXu1D79hbaZuKCIK4mswSP86MtmiIQjuOZnVhPdbjOVILsoQwZUtnmU2xRfX1E0QWuFEI0umhw3mDDn6dNHw==", "path": "microsoft.extensions.options/3.1.7", "hashPath": "microsoft.extensions.options.3.1.7.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tx6gMKE3rDspA1YZT8SlQJmyt1BaBSl6mNjB3g0ZO6m3NnoavCifXkGeBuDk9Ae4XjW8C+dty52p+0u38jPRIQ==", "path": "microsoft.extensions.options.configurationextensions/3.1.0", "hashPath": "microsoft.extensions.options.configurationextensions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "path": "microsoft.extensions.primitives/5.0.0", "hashPath": "microsoft.extensions.primitives.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8XcqYcpcdBAxUhLeyYcuKmxu4CtNQA9IphTnARpQGhkop4A93v2XgM3AtaVVJo3H2cDWxWM6aeO8HxkifREqw==", "path": "microsoft.extensions.webencoders/2.2.0", "hashPath": "microsoft.extensions.webencoders.2.2.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.21.1": {"type": "package", "serviceable": true, "sha512": "sha512-vycgk7S/HAbHaUaK4Tid1fsWHsXdFRRP2KavAIOHCVV27zvuQfYAjXmMvctuuF4egydSumG58CwPZob3gWeYgQ==", "path": "microsoft.identity.client/4.21.1", "hashPath": "microsoft.identity.client.4.21.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+7JIww64PkMt7NWFxoe4Y/joeF7TAtA/fQ0b2GFGcagzB59sKkTt/sMZWR6aSZht5YC7SdHi3W6yM1yylRGJCQ==", "path": "microsoft.identitymodel.jsonwebtokens/6.8.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfh/p4MaN4gkmhPxwbu8IjrmoDncGfHHPh1sTnc0AcM/Oc39/fzC9doKNWvUAjzFb8LqA6lgZyblTrIsX/wDXg==", "path": "microsoft.identitymodel.logging/6.8.0", "hashPath": "microsoft.identitymodel.logging.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-OJZx5nPdiH+MEkwCkbJrTAUiO/YzLe0VSswNlDxJsJD9bhOIdXHufh650pfm59YH1DNevp3/bXzukKrG57gA1w==", "path": "microsoft.identitymodel.protocols/6.8.0", "hashPath": "microsoft.identitymodel.protocols.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-X/PiV5l3nYYsodtrNMrNQIVlDmHpjQQ5w48E+o/D5H4es2+4niEyQf3l03chvZGWNzBRhfSstaXr25/Ye4AeYw==", "path": "microsoft.identitymodel.protocols.openidconnect/6.8.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-gTqzsGcmD13HgtNePPcuVHZ/NXWmyV+InJgalW/FhWpII1D7V1k0obIseGlWMeA4G+tZfeGMfXr0klnWbMR/mQ==", "path": "microsoft.identitymodel.tokens/6.8.0", "hashPath": "microsoft.identitymodel.tokens.6.8.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-RmINcaqiEkawM9C8oxFMN/CZmn1fGKWVsosbSY/8ARUNdHqV47hqhPVbrG3qUqLaRQI5w4HuqFOqrbhoSWcH6w==", "path": "microsoft.netcore.platforms/3.1.1", "hashPath": "microsoft.netcore.platforms.3.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "MongoDB.Bson/2.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-hLk3nlr2YMe8O5LBi6us9JQTEBT/Kabb92eAxgDIXlENS17FOFOmxbGnB5rvKEK1qOaoYWFfyEnLqTt/6hTlpw==", "path": "mongodb.bson/2.10.4", "hashPath": "mongodb.bson.2.10.4.nupkg.sha512"}, "MongoDB.Driver/2.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-RhARXhIRirbmXxNbokYn+TCmZXnVP/0XNQncz8wySmyUNmVGUhNxiHpUniBCu+wIR+LkZ77+tFHzc+38ShSFUg==", "path": "mongodb.driver/2.10.4", "hashPath": "mongodb.driver.2.10.4.nupkg.sha512"}, "MongoDB.Driver.Core/2.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-FF9Matyemmup3wvaicOm/ZKDJITI0mHA1G+9dF+A9lLuU6k3eo6mGvCXgBVPxTXqHHd70AgKwIOFvszIUxqpSA==", "path": "mongodb.driver.core/2.10.4", "hashPath": "mongodb.driver.core.2.10.4.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zkcsra5riDiKXqOiCKskycakpbO5RYkuUWG+z2AZML3A4NShvAs/D3InwkxH0OcbjZQOWo763Hjdmhr0AaylcA==", "path": "mongodb.libmongocrypt/1.0.0", "hashPath": "mongodb.libmongocrypt.1.0.0.nupkg.sha512"}, "Mono.Cecil/0.11.3": {"type": "package", "serviceable": true, "sha512": "sha512-DNYE+io5XfEE8+E+5padThTPHJARJHbz1mhbhMPNrrWGKVKKqj/KEeLvbawAmbIcT73NuxLV7itHZaYCZcVWGg==", "path": "mono.cecil/0.11.3", "hashPath": "mono.cecil.0.11.3.nupkg.sha512"}, "MT.Enterprise.BPM.Reactor/1.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-rlOwDKYlTi7oe8tRrYYTIQs8nu+la5MdAs/kuflPvCBYQMSw4zIVfckoU52PTTagT+dplTgwoZo0zwYFUTyvPA==", "path": "mt.enterprise.bpm.reactor/1.0.10", "hashPath": "mt.enterprise.bpm.reactor.1.0.10.nupkg.sha512"}, "MT.Enterprise.Core/********": {"type": "package", "serviceable": true, "sha512": "sha512-WjoaMHbqzc3m9LEIh9BjiTcb/hg3GhCPx0/3SD3OqqB0g5D3179I7iS6p+wgswkCpW8tcmh7ak2DJVdPTWp86A==", "path": "mt.enterprise.core/********", "hashPath": "mt.enterprise.core.********.nupkg.sha512"}, "MT.Enterprise.SDK/*******": {"type": "package", "serviceable": true, "sha512": "sha512-f0/X0wiFUhbLmkolguvfubbw+ejabkBQaHP4QK/+lQoAAoG7jW06nfjDkYBeUzunEqQ6S2m6ckGUmPtVlNNLgw==", "path": "mt.enterprise.sdk/*******", "hashPath": "mt.enterprise.sdk.*******.nupkg.sha512"}, "MT.Enterprise.Utils/1.0.13": {"type": "package", "serviceable": true, "sha512": "sha512-bnOuQO0cOHCmSg1iFbexXmnGis49ijHuaJTzx1mjRWJ6adcH+mcKXTPn1dN4rtqavIhGQE1m1inAN9SsAfIgww==", "path": "mt.enterprise.utils/1.0.13", "hashPath": "mt.enterprise.utils.1.0.13.nupkg.sha512"}, "MT.Enterprise.Vision/0.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-TFJOBbxQTFRQLvqi5s8LBTGPK40r8bCrIwTNJ3XELSlz5zyoerueX8ChjIAkzWOy4kbWE5yNw+BVvsHwxJAqfA==", "path": "mt.enterprise.vision/0.0.3", "hashPath": "mt.enterprise.vision.0.0.3.nupkg.sha512"}, "MT.SqlSugar/*******": {"type": "package", "serviceable": true, "sha512": "sha512-bUCdHTVgfRFcqVlq4tryWBXFtqURb/cPYKhx2VMkQFpDi+yJ3/IyBRdzzfqh0bAn05GY4WC657184KY/FDuPKw==", "path": "mt.sqlsugar/*******", "hashPath": "mt.sqlsugar.*******.nupkg.sha512"}, "MySql.Data/8.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-ZVcctmag6cX0DtDxWh3fhBlSdZfGHKIUIodl7QtjZKupSgbCT0X+bO1Faw/NfUR2VfoxB/L9WzLfeCvki65qVg==", "path": "mysql.data/8.0.31", "hashPath": "mysql.data.8.0.31.nupkg.sha512"}, "MySqlConnector/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-bnQk6joP7z0k1aIk13BjrZXIakN+UobXjO8EMh/Y+L6vZ6ul9cJ8RXkD9tia86ksgFs9GqmvPgX7ILx7mOpwNQ==", "path": "mysqlconnector/1.0.1", "hashPath": "mysqlconnector.1.0.1.nupkg.sha512"}, "nacos-sdk-csharp-unofficial/0.8.5": {"type": "package", "serviceable": true, "sha512": "sha512-P6XtcilVILlPK2CoMQMKm86ndV5ITs4rcKel5FA0Z/b/+XBMvDX+DwKn5FU6q7yrOmsG61dRTDZ9RTkeaZY0Zg==", "path": "nacos-sdk-csharp-unofficial/0.8.5", "hashPath": "nacos-sdk-csharp-unofficial.0.8.5.nupkg.sha512"}, "nacos-sdk-csharp-unofficial.AspNetCore/0.8.5": {"type": "package", "serviceable": true, "sha512": "sha512-MRTYmIZKOYOr3KscNhtUuzPCifu/2ewzpqxs/fBIBDtUOKxIeYUk6R+IaKPv1kaYsKigXQNatEvZva289RhC+w==", "path": "nacos-sdk-csharp-unofficial.aspnetcore/0.8.5", "hashPath": "nacos-sdk-csharp-unofficial.aspnetcore.0.8.5.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5PYT/IqQ+UK31AmZiSS102R6EsTo+LGTSI8bp7WAUqDKaF4wHXD8U9u4WxTI1vc64tYi++8p3dk3WWNqPFgldw==", "path": "newtonsoft.json.bson/1.0.1", "hashPath": "newtonsoft.json.bson.1.0.1.nupkg.sha512"}, "NLog/4.7.4": {"type": "package", "serviceable": true, "sha512": "sha512-gSGX/ur0P3jp17H37HW7ycXR6L1578tQbilf4HLX1t2viCDFgtwEsfHMPf0/jcuf7/qqGgoU/vKgyQ9hzxuOEQ==", "path": "nlog/4.7.4", "hashPath": "nlog.4.7.4.nupkg.sha512"}, "NLog.Extensions.Logging/1.6.5": {"type": "package", "serviceable": true, "sha512": "sha512-f+7W4DhNkpFdg2X6fNR8Ts06TSM9Hd2BdfXem3esTFGR9cAVdQRPwf/N0H0Fc1vidrLJVETkmywXG/0Qz/lxjw==", "path": "nlog.extensions.logging/1.6.5", "hashPath": "nlog.extensions.logging.1.6.5.nupkg.sha512"}, "NLog.Mongo/*********": {"type": "package", "serviceable": true, "sha512": "sha512-/6eGCYJeIMJf/LFjYJsaOuxMBIJENQbT1uWM7VhIPJwyJJMcX93VQrT/a2k1KfMWyOz9ztG0rhaKQiS2RoJcLg==", "path": "nlog.mongo/*********", "hashPath": "nlog.mongo.*********.nupkg.sha512"}, "Npgsql/5.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-EQWwxb2lN9w78YG4f6Fxhw5lFEx4LuaNGasXzw86kTOJxiPsUORSh/BTencNZJO4uVqGZx3EO9Z8JXTAvRjgeg==", "path": "npgsql/5.0.7", "hashPath": "npgsql.5.0.7.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/3.21.1": {"type": "package", "serviceable": true, "sha512": "sha512-SJM0qRVz6a7xMJtPPHAObq7MEzo42T+6+MImuuUK7ZCTXc2BIXbc9cenN7006FcOuX8x4OeTpPbFfQTVlhk9bw==", "path": "oracle.manageddataaccess.core/3.21.1", "hashPath": "oracle.manageddataaccess.core.3.21.1.nupkg.sha512"}, "Oscar.Data.SqlClient/4.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-VJ3xVvRjxrPi/mMPT5EqYiMZor0MjFu83mw1qvUveBFWJSudGh9BOKZq7RkhqeNCcL1ud0uK0/TVkw+xTa4q4g==", "path": "oscar.data.sqlclient/4.0.4", "hashPath": "oscar.data.sqlclient.4.0.4.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7hzHplEIVOGBl5zOQZGX/DiJDHjq+RVRVrYgDiqXb6RriqWAdacXxp+XO9WSrATCEXyNOUOQg9aqQArsjase/A==", "path": "pipelines.sockets.unofficial/2.2.0", "hashPath": "pipelines.sockets.unofficial.2.2.0.nupkg.sha512"}, "Portable.BouncyCastle/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-eZZBCABzVOek+id9Xy04HhmgykF0wZg9wpByzrWN7q8qEI0Qen9b7tfd7w8VA3dOeesumMG7C5ZPy0jk7PSRHw==", "path": "portable.bouncycastle/1.9.0", "hashPath": "portable.bouncycastle.1.9.0.nupkg.sha512"}, "RabbitMQ.Client/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-kdcin0uz1b9xQPvlLOoa8Y2zQsfu7nRuTrcX02dCLVzwGVlsPp9AFgu1L3kRh4wSFEOGE03OVqpWKxaDlhuowQ==", "path": "rabbitmq.client/6.2.1", "hashPath": "rabbitmq.client.6.2.1.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Security/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-M2nN92ePS8BgQ2oi6Jj3PlTUzadYSIWLdZrHY1n1ZcW9o4wAQQ6W+aQ2lfq1ysZQfVCgDwY58alUdowrzezztg==", "path": "runtime.native.system.net.security/4.3.0", "hashPath": "runtime.native.system.net.security.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "SharpCompress/0.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-HBbT47JHvNrsZX2dTBzUBOSzBt+EmIRGLIBkbxcP6Jef7DB4eFWQX5iHWV3Nj7hABFPCjISrZ8s0z72nF2zFHQ==", "path": "sharpcompress/0.23.0", "hashPath": "sharpcompress.0.23.0.nupkg.sha512"}, "SharpZipLib/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YuYztmY3jEb21F6e5LIPHJjApdtzdCPQ284UzsCKNfkgW71bukFHJES6RbKi+wm053XzFg0LX5/2vj/9gl8F/g==", "path": "sharpziplib/1.0.0", "hashPath": "sharpziplib.1.0.0.nupkg.sha512"}, "SmartFormat.NET/2.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-5G4Y7v0Nq+jQe7I4i1DOPlG4DeiFz6chU+0p5NH7Gh6SFNbDxCQxoLCPNg5JtB9jXpv9pSocYCdmfumHnABvSg==", "path": "smartformat.net/2.4.2", "hashPath": "smartformat.net.2.4.2.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-f5U8Sw0lRym8tTraJ2zm6OqcDrcrEVvcKDtYlKSLs3Ox9SerkwkPXiFXb/uiW0g2tJdUw6oBhsxI/l5DoRxXMg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.0.4", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.core/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-4XlDZpDAsboMD6qZQcz9AaKblKDUTVHF+8f3lvbP7QjoqSRr2Xc0Lm34IK2pjRIYnyFLhI3yOJ5YWfOiCid2yg==", "path": "sqlitepclraw.core/2.0.4", "hashPath": "sqlitepclraw.core.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-oetvmtDZOE4Nnrtxd8Trapl9geBiu0rDCUXff46qGYjnUwzaU1mZ3OHnfR402tl32rx8gBWg3n5OBRaPJRbsGw==", "path": "sqlitepclraw.lib.e_sqlite3/2.0.4", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.provider.dynamic_cdecl/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-AY6+vv/4ji1mCkLrS6HP/88rHT9YFKRyg3LUj8RyIk6imJMUFdQDiP8rK8gq0a/0FbqspLjK1t7rtKcr7FXRYA==", "path": "sqlitepclraw.provider.dynamic_cdecl/2.0.4", "hashPath": "sqlitepclraw.provider.dynamic_cdecl.2.0.4.nupkg.sha512"}, "SqlSugarCore.Dm/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TCZRpNQ21lZqTnBFbuVOKIFWMvl2IFRiU5FcSWbyOVD/F9tSwRK9BUQXtrBh3xpn2v/cUcRJgQdNEknWNjFd6w==", "path": "sqlsugarcore.dm/1.0.0", "hashPath": "sqlsugarcore.dm.1.0.0.nupkg.sha512"}, "SqlSugarCore.Kdbndp/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xQpxN40OUlg3FZnypcXBCFvNl1ndueOjW4Wc+lnk2YjCK+sKjKr/YejJY/DuT/WthY+Z5p086K6igUTX9ZHg2Q==", "path": "sqlsugarcore.kdbndp/1.0.0", "hashPath": "sqlsugarcore.kdbndp.1.0.0.nupkg.sha512"}, "StackExchange.Redis/2.2.88": {"type": "package", "serviceable": true, "sha512": "sha512-JJi1jcO3/ZiamBhlsC/TR8aZmYf+nqpGzMi0HRRCy5wJkUPmMnRp0kBA6V84uhU8b531FHSdTDaFCAyCUJomjA==", "path": "stackexchange.redis/2.2.88", "hashPath": "stackexchange.redis.2.2.88.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/1.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-B43<PERSON>sz5EfMwyEbnObwRxW5u85fzJma3lrDeGcSAV1qkhSRTNY5uXAByTn9h9ddNdhM+4/YoLc/CI43umjwIl9Q==", "path": "system.collections.immutable/1.7.1", "hashPath": "system.collections.immutable.1.7.1.nupkg.sha512"}, "System.Collections.NonGeneric/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hMxFT2RhhlffyCdKLDXjx8WEC5JfCvNozAZxCablAuFRH74SCV4AgzE8yJCh/73bFnEoZgJ9MJmkjQ0dJmnKqA==", "path": "system.collections.nongeneric/4.0.1", "hashPath": "system.collections.nongeneric.4.0.1.nupkg.sha512"}, "System.Collections.Specialized/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/HKQyVP0yH1I0YtK7KJL/28snxHNH/bi+0lgk/+MbURF6ULhAE31MDI+NZDerNWu264YbxklXCCygISgm+HMug==", "path": "system.collections.specialized/4.0.1", "hashPath": "system.collections.specialized.4.0.1.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "path": "system.componentmodel.annotations/4.7.0", "hashPath": "system.componentmodel.annotations.4.7.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-sc/7eVCdxPrp3ljpgTKVaQGUXiW05phNWvtv/m2kocXqrUQvTVWKou1Edas2aDjTThLPZOxPYIGNb/HN0QjURg==", "path": "system.componentmodel.primitives/4.1.0", "hashPath": "system.componentmodel.primitives.4.1.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-MnDAlaeJZy9pdB5ZdOlwdxfpI+LJQ6e0hmH7d2+y2LkiD8DRJynyDYl4Xxf3fWFm7SbEwBZh4elcfzONQLOoQw==", "path": "system.componentmodel.typeconverter/4.1.0", "hashPath": "system.componentmodel.typeconverter.4.1.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "path": "system.configuration.configurationmanager/6.0.0", "hashPath": "system.configuration.configurationmanager.6.0.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Data.OleDb/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LQ8PjTIF1LtrrlGiyiTVjAkQtTWKm9GSNnygIlWjhN9y88s7xhy6DUNDDkmQQ9f6ex7mA4k0Tl97lz/CklaiLg==", "path": "system.data.oledb/6.0.0", "hashPath": "system.data.oledb.6.0.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-j81Lovt90PDAq8kLpaJfJKV/rWdWuEk6jfV+MBkee33vzYLEUsy4gXK8laa9V2nZlLM9VM9yA/OOQxxPEJKAMw==", "path": "system.diagnostics.diagnosticsource/4.7.1", "hashPath": "system.diagnostics.diagnosticsource.4.7.1.nupkg.sha512"}, "System.Diagnostics.FileVersionInfo/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-omCF64wzQ3Q2CeIqkD6lmmxeMZtGHUmzgFMPjfVaOsyqpR66p/JaZzManMw1s33osoAb5gqpncsjie67+yUPHQ==", "path": "system.diagnostics.fileversioninfo/4.3.0", "hashPath": "system.diagnostics.fileversioninfo.4.3.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gbeE5tNp/oB7O8kTTLh3wPPJCxpNOphXPTWVs1BsYuFOYapFijWuh0LYw1qnDo4gwDUYPXOmpTIhvtxisGsYOQ==", "path": "system.diagnostics.performancecounter/6.0.0", "hashPath": "system.diagnostics.performancecounter.6.0.0.nupkg.sha512"}, "System.Diagnostics.Process/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mpVZ5bnlSs3tTeJ6jYyDJEIa6tavhAd88lxq1zbYhkkCu0Pno2+gHXcvZcoygq2d8JxW3gojXqNJMTAshduqZA==", "path": "system.diagnostics.process/4.1.0", "hashPath": "system.diagnostics.process.4.1.0.nupkg.sha512"}, "System.Diagnostics.StackTrace/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiHg0vgtd35/DM9jvtaC1eKRpWZxr0gcQd643ABG7GnvSlf5pOkY2uyd42mMOJoOmKvnpNj0F4tuoS1pacTwYw==", "path": "system.diagnostics.stacktrace/4.3.0", "hashPath": "system.diagnostics.stacktrace.4.3.0.nupkg.sha512"}, "System.Diagnostics.TextWriterTraceListener/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w36Dr8yKy8xP150qPANe7Td+/zOI3G62ImRcHDIEW+oUXUuTKZHd4DHmqRx5+x8RXd85v3tXd1uhNTfsr+yxjA==", "path": "system.diagnostics.textwritertracelistener/4.0.0", "hashPath": "system.diagnostics.textwritertracelistener.4.0.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "path": "system.diagnostics.tracesource/4.3.0", "hashPath": "system.diagnostics.tracesource.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.DirectoryServices/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NRENC4ulDamI4DQtrYybxtQU3qnhGSTUdEKJkLyctHXY4RqNyS/egZpB9z8/CnFCiaQZmwLlqxfBmw80VlKBTA==", "path": "system.directoryservices/4.7.0", "hashPath": "system.directoryservices.4.7.0.nupkg.sha512"}, "System.DirectoryServices.Protocols/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-yy0a+E/yksdoMWfZEmWpI5LuCbJ/E6P5d4QRbqUDj/xC4MV7Vw5DiW3KREA9LFbWedoGx90KikUfSN0xhE1j1g==", "path": "system.directoryservices.protocols/4.7.0", "hashPath": "system.directoryservices.protocols.4.7.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-5tBCjAub2Bhd5qmcd0WhR5s354e4oLYa//kOWrkX+6/7ZbDDJjMTfwLSOiZ/MMpWdE4DWPLOfTLOq/juj9CKzA==", "path": "system.identitymodel.tokens.jwt/6.8.0", "hashPath": "system.identitymodel.tokens.jwt.6.8.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-vMToiarpU81LR1/KZtnT7VDPvqAZfw9oOS5nY6pPP78nGYz3COLsQH3OfzbR+SjTgltd31R6KmKklz/zDpTmzw==", "path": "system.io.filesystem.accesscontrol/4.7.0", "hashPath": "system.io.filesystem.accesscontrol.4.7.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-irMYm3vhVgRsYvHTU5b2gsT2CwT/SMM6LZFzuJjpIvT5Z4CshxNsaoBC1X/LltwuR3Opp8d6jOS/60WwOb7Q2Q==", "path": "system.io.pipelines/5.0.0", "hashPath": "system.io.pipelines.5.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-kXXqUk3nBlYGzPhQ2fx+5ybh2vN0u1QsfhBR1LmofYwNyGllCOUUV9bPygmXGUFfebLeKaEF1uSLWgBUEhj8oQ==", "path": "system.linq.dynamic.core/1.0.9", "hashPath": "system.linq.dynamic.core.1.0.9.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Yn/WfYe9RoRfmSLvUt2JerP0BTGGykCZkQPgojaxgzF2N0oPo+/AhB8TXOpdCcNlrG3VRtsamtK2uzsp3cqRVw==", "path": "system.linq.queryable/4.0.1", "hashPath": "system.linq.queryable.4.0.1.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.NameResolution/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AFYl08R7MrsrEjqpQWTZWBadqXyTzNDaWpMqyxhb0d6sGhV6xMDKueuBXlLL30gz+DIRY6MpdgnHWlCh5wmq9w==", "path": "system.net.nameresolution/4.3.0", "hashPath": "system.net.nameresolution.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Security/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xT2jbYpbBo3ha87rViHoTA6WdvqOAW37drmqyx/6LD8p7HEPT2qgdxoimRzWtPg8Jh4X5G9BV2seeTv4x6FYlA==", "path": "system.net.security/4.3.2", "hashPath": "system.net.security.4.3.2.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-AucBYo3DSI0IDxdUjKksBcQJXPHyoPyrCXYURW1WDsLI4M65Ar/goSHjdnHOAY9MiYDNKqDlIgaYm+zL2hA1KA==", "path": "system.reflection.emit.ilgeneration/4.7.0", "hashPath": "system.reflection.emit.ilgeneration.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "path": "system.runtime.caching/4.7.0", "hashPath": "system.runtime.caching.4.7.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Formatters/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KT591AkTNFOTbhZlaeMVvfax3RqhH1EJlcwF50Wm7sfnBLuHiOeZRRKrr1ns3NESkM20KPZ5Ol/ueMq5vg4QoQ==", "path": "system.runtime.serialization.formatters/4.3.0", "hashPath": "system.runtime.serialization.formatters.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-TGQX51gxpY3K3I6LJlE2LAftVlIMqJf0cBGhz68Y89jjk3LJCB6SrwiD+YN1fkqemBvWGs+GjyMJukl6d6goyQ==", "path": "system.security.cryptography.pkcs/4.5.0", "hashPath": "system.security.cryptography.pkcs.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-i2Jn6rGXR63J0zIklImGRkDIJL4b1NfPSEbIVHBlqoIb12lfXIigCbDRpDmIEzwSo/v1U5y/rYJdzZYSyCWxvg==", "path": "system.security.cryptography.xml/4.5.0", "hashPath": "system.security.cryptography.xml.4.5.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Security.SecureString/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-sqzq9GD6/b0yqPuMpgIKBuoLf4VKAj8oAfh4kXSzPaN6eoKY3hRi9C5L27uip25qlU+BGPfb0xh2Rmbwc4jFVA==", "path": "system.security.securestring/4.0.0", "hashPath": "system.security.securestring.4.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-i2fOvznVVgOOTLUz8FgSap/MsR98I4Iaoz99VXcOW/e7Y2OdY42zhYpBYpZyivk5alYY/UsOWAVswhtjxceodA==", "path": "system.text.encoding.codepages/4.7.1", "hashPath": "system.text.encoding.codepages.4.7.1.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xg4G4Indi4dqP1iuAiMSwpiWS54ZghzR644OtsRCm/m/lBMG8dUBhLVN7hLm8NNrNTR+iGbshCPTwrvxZPlm4g==", "path": "system.text.encodings.web/4.5.0", "hashPath": "system.text.encodings.web.4.5.0.nupkg.sha512"}, "System.Text.Json/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+luxMQNZ2WqeffBU7Ml6njIvxc8169NW2oU+ygNudXQGZiarjE7DOtN7bILiQjTZjkmwwRZGTtLzmdrSI/Ustw==", "path": "system.text.json/5.0.0", "hashPath": "system.text.json.5.0.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Channels/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-6akRtHK/wab3246t4p5v3HQrtQk8LboOt5T4dtpNgsp3zvDeM4/Gx8V12t0h+c/W9/enUrilk8n6EQqdQorZAA==", "path": "system.threading.channels/4.7.1", "hashPath": "system.threading.channels.4.7.1.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-WSKUTtLhPR8gllzIWO2x6l4lmAIfbyMAiTlyXAis4QBDonXK4b4S6F8zGARX4/P8wH3DH+sLdhamCiHn+fTU1A==", "path": "system.threading.tasks.extensions/4.5.1", "hashPath": "system.threading.tasks.extensions.4.5.1.nupkg.sha512"}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbjBNZHf/vQCfcdhzx7knsiygoCKgxL8mZOeocXZn5gWhCdzHIq6bYNKWX0LAJCWYP7bds4yBK8p06YkP0oa0g==", "path": "system.threading.tasks.parallel/4.3.0", "hashPath": "system.threading.tasks.parallel.4.3.0.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.ValueTuple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cNLEvBX3d6MMQRZe3SMFNukVbitDAEpVZO17qa0/2FHxZ7Y7PpFRpr6m2615XYM/tYYYf0B+WyHNujqIw8Luwg==", "path": "system.valuetuple/4.3.0", "hashPath": "system.valuetuple.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jw9oHHEIVW53mHY9PgrQa98Xo2IZ0ZjrpdOTmtvk+Rvg4tq7dydmxdNqUvJ5YwjDqhn75mBXWttWjiKhWP53LQ==", "path": "system.xml.xpath.xdocument/4.3.0", "hashPath": "system.xml.xpath.xdocument.4.3.0.nupkg.sha512"}, "Medusa.Service.Modeling.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Medusa.Service.Modeling.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}