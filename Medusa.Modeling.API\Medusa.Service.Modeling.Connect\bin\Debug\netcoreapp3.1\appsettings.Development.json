{"Nacos": {"ServerAddresses": ["http://*************:8848"], "DefaultTimeOut": 15000, "Namespace": "boost_prd", "ListenInterval": 1000, "LanguageGroupId": "i18n", "LanguageIds": ["en.json", "zh.json"]}, "Logging": {"LogLevel": {"Default": "Debug", "System": "Information", "Microsoft": "Information"}}, "Persistence": {"DbType": "Mysql", "ConnectionString": "server=**************;Database=LowCode;Uid=root;Pwd=*******"}, "Boost": {"DbType": "Mysql", "ConnectionString": "server=**************;Database=boost;Uid=root;Pwd=*******"}, "ApiGateway": {"Host": "*************", "Port": "10050", "AppKey": "799e6e124ad95e09b055ae8c8cc53d7f", "AppSecret": "a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0", "Timeout": "5"}, "ApiRequest": {"Host": "*************:6379", "DB": 8, "Password": "architecture", "Timeout": "30", "IsHttps": false}, "EncryptKeys": {"BPM": "58e13310360446198c1c596f32ad86c6"}, "PlatformSettings": {"ProductLicense": "/platform/v1/product-license/{id}"}, "ProcessSettings": {"BusinessObject": "/process/v1/manage/processes/{id}/business-object", "StartProcess": "/process/endpoints/business-data", "RecallProcess": "/process/endpoints/{procinstno}/recall", "CancelProcess": "/process/endpoints/{procinstno}/cancel", "StartUrl": "http://*************:10081/customer/start?bsid={bsid}&btid={btid}&boid={boid}", "ReStartUrl": "http://*************:10081/customer/my-processes/{procinstno}?bsid={bsid}&btid={btid}&boid={boid}"}, "Redis": {"Host": "*************:6379", "DB": 0, "Password": "architecture"}, "Message": {"Url": "http://*************:31608/v1/messages/Wf_EOPNotifyMessage", "SystemCode": "crm"}, "RabbitMQ": {"HostName": "*************", "Port": 5672, "UserName": "movitech", "Password": "movitech", "InterfaceQueue": "modeling.connect"}}