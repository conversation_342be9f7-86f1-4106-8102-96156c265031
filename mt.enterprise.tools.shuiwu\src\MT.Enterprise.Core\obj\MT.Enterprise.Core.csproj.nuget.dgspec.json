{"format": 1, "restore": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools.shuiwu\\src\\MT.Enterprise.Core\\MT.Enterprise.Core.csproj": {}}, "projects": {"D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools.shuiwu\\src\\MT.Enterprise.Core\\MT.Enterprise.Core.csproj": {"version": "1.0.16.4", "restore": {"projectUniqueName": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools.shuiwu\\src\\MT.Enterprise.Core\\MT.Enterprise.Core.csproj", "projectName": "MT.Enterprise.Core.ShuiWu", "projectPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools.shuiwu\\src\\MT.Enterprise.Core\\MT.Enterprise.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\Bpm_Chinahho\\BPM-M\\mt.enterprise.tools.shuiwu\\src\\MT.Enterprise.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\BPM\\BPM平台\\Pakages": {}, "D:\\项目\\Bpm_Chinahho\\BPM\\NugetPakage": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"AspectCore.Core": {"target": "Package", "version": "[2.2.0, )"}, "AspectCore.Extensions.DependencyInjection": {"target": "Package", "version": "[2.1.0, )"}, "Exceptionless.NLog": {"target": "Package", "version": "[5.0.0, )"}, "MT.SqlSugar": {"target": "Package", "version": "[1.3.2, )"}, "Microsoft.AspNetCore.Hosting": {"target": "Package", "version": "[2.2.7, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.2.5, )"}, "Microsoft.Extensions.Caching.Abstractions": {"target": "Package", "version": "[3.1.7, )"}, "NLog.Database": {"target": "Package", "version": "[5.0.5, )"}, "NLog.MongoDB.NetCore": {"target": "Package", "version": "[1.0.0, )"}, "NLog.Web.AspNetCore": {"target": "Package", "version": "[5.2.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.6.90, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[1.1.118, )"}, "nacos-sdk-csharp.AspNetCore": {"target": "Package", "version": "[1.3.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.303\\RuntimeIdentifierGraph.json"}}}}}